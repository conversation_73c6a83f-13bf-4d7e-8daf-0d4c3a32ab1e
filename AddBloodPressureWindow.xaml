<Window x:Class="WpfAdmin.AddBloodPressureWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="血压录入" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="Transparent"
        AllowsTransparency="True"
        WindowStyle="None"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- 现代化输入框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#5D6EFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3D4BD9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6C757D"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#5A6268"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#545B62"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 删除按钮样式 -->
        <Style x:Key="DeleteButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="FontSize" Value="12"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 现代化日期选择器样式 -->
        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DatePicker">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 日期显示文本框 -->
                                <DatePickerTextBox x:Name="PART_TextBox"
                                                  Grid.Column="0"
                                                  Background="Transparent"
                                                  Foreground="White"
                                                  BorderThickness="0"
                                                  Padding="{TemplateBinding Padding}"
                                                  FontSize="{TemplateBinding FontSize}"
                                                  FontWeight="{TemplateBinding FontWeight}"
                                                  VerticalContentAlignment="Center"
                                                  IsReadOnly="True"/>

                                <!-- 日历按钮 -->
                                <Button x:Name="PART_Button"
                                       Grid.Column="1"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Width="30"
                                       Height="30"
                                       Margin="5,0"
                                       Cursor="Hand">
                                    <TextBlock Text="&#xE787;"
                                              FontFamily="Segoe MDL2 Assets"
                                              FontSize="16"
                                              Foreground="#A0A0FF"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="4">
                                                            <ContentPresenter HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#4A5AE8"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>

                                <!-- 日历弹出窗口 -->
                                <Popup x:Name="PART_Popup"
                                      AllowsTransparency="True"
                                      Placement="Bottom"
                                      PlacementTarget="{Binding ElementName=PART_TextBox}"
                                      StaysOpen="False">
                                    <Border Background="#293153"
                                           CornerRadius="8"
                                           BorderThickness="1"
                                           BorderBrush="#5D6EFF"
                                           Margin="0,5,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect ShadowDepth="3" BlurRadius="10" Opacity="0.3"/>
                                        </Border.Effect>
                                        <Calendar x:Name="PART_Calendar"
                                                 Background="Transparent"
                                                 Foreground="White"/>
                                    </Border>
                                </Popup>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 记录项样式 -->
        <Style x:Key="RecordItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,5"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#4A5AE8"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Border Background="#293153" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="5" BlurRadius="15" Opacity="0.3" Color="Black"/>
        </Border.Effect>

        <Grid Margin="30" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="&#xE95E;" FontFamily="Segoe MDL2 Assets"
                               FontSize="24" Foreground="#5D6EFF"
                               VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock Text="血压录入" FontSize="24"
                               Foreground="White" FontWeight="Medium"
                               VerticalAlignment="Center"/>
                    <TextBlock x:Name="DateDisplayText" Text="" FontSize="16"
                               Foreground="#A0A0FF" Margin="20,0,0,0" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="1" x:Name="CloseButton"
                        Width="30" Height="30"
                        Background="Transparent"
                        BorderThickness="0"
                        Click="CloseButton_Click"
                        Cursor="Hand">
                    <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                               FontSize="16" Foreground="#8890AD"/>
                </Button>
            </Grid>

            <!-- 表单内容 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="400"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧：录入区域 -->
                    <StackPanel Grid.Column="0" Margin="0,0,20,0">
                        <!-- 日期选择 -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="日期" Foreground="White" FontSize="14"
                                       FontWeight="Medium" Margin="0,0,0,8"/>
                            <DatePicker x:Name="DatePicker"
                                       Style="{StaticResource ModernDatePicker}"/>
                        </StackPanel>

                        <!-- 血压输入 -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="血压测量" Foreground="White" FontSize="14"
                                       FontWeight="Medium" Margin="0,0,0,8"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="高压 (收缩压)" Foreground="#A0A0FF"
                                               FontSize="12" Margin="0,0,0,5"/>
                                    <TextBox x:Name="SystolicTextBox"
                                            Style="{StaticResource ModernTextBox}"
                                            TextChanged="BloodPressure_TextChanged"/>
                                </StackPanel>

                                <TextBlock Grid.Column="1" Text="/" FontSize="20" FontWeight="Bold"
                                           Foreground="White" VerticalAlignment="Center" Margin="10,15,10,0"/>

                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="低压 (舒张压)" Foreground="#A0A0FF"
                                               FontSize="12" Margin="0,0,0,5"/>
                                    <TextBox x:Name="DiastolicTextBox"
                                            Style="{StaticResource ModernTextBox}"
                                            TextChanged="BloodPressure_TextChanged"/>
                                </StackPanel>
                            </Grid>
                            <TextBlock Text="正常血压范围：90-120/60-80 mmHg"
                                       Foreground="#8890AD" FontSize="12"
                                       Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- 心率输入（可选） -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="心率（可选）" Foreground="White" FontSize="14"
                                       FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBox x:Name="HeartRateTextBox"
                                    Style="{StaticResource ModernTextBox}"/>
                            <TextBlock Text="正常心率范围：60-100 次/分"
                                       Foreground="#8890AD" FontSize="12"
                                       Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- 备注 -->
                        <StackPanel Margin="0,0,0,20">
                            <TextBlock Text="备注" Foreground="White" FontSize="14"
                                       FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBox x:Name="NotesTextBox"
                                    Style="{StaticResource ModernTextBox}"
                                    Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                        </StackPanel>

                        <!-- 血压状态提示 -->
                        <Border x:Name="BloodPressureWarning" Background="#FF725E"
                                CornerRadius="8" Padding="15" Margin="0,0,0,20"
                                Visibility="Collapsed">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE7BA;" FontFamily="Segoe MDL2 Assets"
                                           FontSize="16" Foreground="White" VerticalAlignment="Center"/>
                                <TextBlock x:Name="BloodPressureWarningText" Text="" FontSize="14"
                                           Foreground="White" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- 操作按钮 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0" x:Name="SaveButton" Content="保存记录"
                                    Style="{StaticResource ModernButton}" Click="SaveButton_Click"
                                    Margin="0,0,10,0"/>
                            <Button Grid.Column="1" Content="取消" Style="{StaticResource CancelButton}"
                                    Click="CancelButton_Click" Margin="10,0,0,0"/>
                        </Grid>
                    </StackPanel>

                    <!-- 右侧：今日记录列表 -->
                    <StackPanel Grid.Column="1">
                        <!-- 标题 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                            <TextBlock Text="&#xE823;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="16" Foreground="#5D6EFF" VerticalAlignment="Center"/>
                            <TextBlock Text="今日血压记录" FontSize="16" FontWeight="Medium"
                                       Foreground="White" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            <TextBlock x:Name="RecordCountText" Text="(0条记录)" FontSize="14"
                                       Foreground="#8890AD" Margin="10,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- 记录列表 -->
                        <ScrollViewer Height="400" VerticalScrollBarVisibility="Auto">
                            <StackPanel x:Name="RecordsPanel">
                                <!-- 记录项将在代码中动态添加 -->
                                <TextBlock x:Name="NoRecordsText" Text="今日暂无血压记录"
                                           FontSize="14" Foreground="#8890AD"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Margin="0,50,0,0"/>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Grid>
            </ScrollViewer>

        </Grid>
    </Border>
</Window>
