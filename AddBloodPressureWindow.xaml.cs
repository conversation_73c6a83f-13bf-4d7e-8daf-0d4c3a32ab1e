using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using WpfAdmin.Models;
using WpfAdmin.Services;

namespace WpfAdmin
{
    public partial class AddBloodPressureWindow : Window
    {
        private readonly BloodPressureService _bloodPressureService;
        public bool DataSaved { get; private set; } = false;
        private List<BloodPressureRecord> _todayRecords = new List<BloodPressureRecord>();

        public AddBloodPressureWindow()
        {
            try
            {
                InitializeComponent();
                _bloodPressureService = new BloodPressureService();

                // 设置默认日期为今天
                DatePicker.SelectedDate = DateTime.Today;
                UpdateDateDisplay();

                // 绑定事件
                DatePicker.SelectedDateChanged += DatePicker_SelectedDateChanged;

                // 窗口加载完成后加载数据
                Loaded += AddBloodPressureWindow_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化血压录入窗口失败: {ex.Message}\n\n详细信息: {ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void AddBloodPressureWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadTodayRecords();
        }

        private void UpdateDateDisplay()
        {
            try
            {
                if (DatePicker?.SelectedDate.HasValue == true)
                {
                    var selectedDate = DatePicker.SelectedDate.Value;
                    if (selectedDate.Date == DateTime.Today)
                    {
                        if (DateDisplayText != null)
                            DateDisplayText.Text = "- 今天";
                    }
                    else
                    {
                        if (DateDisplayText != null)
                            DateDisplayText.Text = $"- {selectedDate:yyyy年MM月dd日}";
                    }
                }
                else
                {
                    if (DateDisplayText != null)
                        DateDisplayText.Text = "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新日期显示失败: {ex.Message}");
            }
        }

        private async void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateDateDisplay();
            await LoadTodayRecords();
        }

        private async System.Threading.Tasks.Task LoadTodayRecords()
        {
            try
            {
                if (!DatePicker.SelectedDate.HasValue) return;

                var selectedDate = DatePicker.SelectedDate.Value;
                _todayRecords = await _bloodPressureService.GetRecordsByDateAsync(selectedDate);

                UpdateRecordsDisplay();
                UpdateStatusText();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载血压记录失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void UpdateRecordsDisplay()
        {
            // 清空现有记录显示
            RecordsPanel.Children.Clear();

            if (!_todayRecords.Any())
            {
                NoRecordsText.Visibility = Visibility.Visible;
                RecordCountText.Text = "(0条记录)";
                RecordsPanel.Children.Add(NoRecordsText);
                return;
            }

            NoRecordsText.Visibility = Visibility.Collapsed;
            RecordCountText.Text = $"({_todayRecords.Count}条记录)";

            foreach (var record in _todayRecords)
            {
                var recordItem = CreateRecordItem(record);
                RecordsPanel.Children.Add(recordItem);
            }
        }

        private Border CreateRecordItem(BloodPressureRecord record)
        {
            var border = new Border
            {
                Style = (Style)FindResource("RecordItemStyle")
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // 左侧信息
            var leftPanel = new StackPanel();

            // 时间和血压值
            var timeAndBpPanel = new StackPanel { Orientation = Orientation.Horizontal };

            var timeText = new TextBlock
            {
                Text = record.RecordTime.ToString("HH:mm"),
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(73, 80, 87)),
                VerticalAlignment = VerticalAlignment.Center
            };

            var bpText = new TextBlock
            {
                Text = $"{record.SystolicPressure}/{record.DiastolicPressure}",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = record.IsAbnormal ?
                    new SolidColorBrush(Color.FromRgb(220, 53, 69)) :
                    new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Margin = new Thickness(15, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var unitText = new TextBlock
            {
                Text = "mmHg",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                Margin = new Thickness(5, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            timeAndBpPanel.Children.Add(timeText);
            timeAndBpPanel.Children.Add(bpText);
            timeAndBpPanel.Children.Add(unitText);

            leftPanel.Children.Add(timeAndBpPanel);

            // 血压等级和心率
            var detailPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 5, 0, 0) };

            var levelText = new TextBlock
            {
                Text = record.BloodPressureLevel,
                FontSize = 12,
                Foreground = record.IsAbnormal ?
                    new SolidColorBrush(Color.FromRgb(220, 53, 69)) :
                    new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                FontWeight = FontWeights.SemiBold
            };

            detailPanel.Children.Add(levelText);

            if (record.HeartRate.HasValue)
            {
                var heartRateText = new TextBlock
                {
                    Text = $"心率: {record.HeartRate}次/分",
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Margin = new Thickness(15, 0, 0, 0)
                };
                detailPanel.Children.Add(heartRateText);
            }

            leftPanel.Children.Add(detailPanel);

            // 备注
            if (!string.IsNullOrEmpty(record.Notes))
            {
                var notesText = new TextBlock
                {
                    Text = record.Notes,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(108, 117, 125)),
                    Margin = new Thickness(0, 5, 0, 0),
                    TextWrapping = TextWrapping.Wrap
                };
                leftPanel.Children.Add(notesText);
            }

            Grid.SetColumn(leftPanel, 0);
            grid.Children.Add(leftPanel);

            // 右侧删除按钮
            var deleteButton = new Button
            {
                Content = "删除",
                Style = (Style)FindResource("DeleteButton"),
                Tag = record,
                VerticalAlignment = VerticalAlignment.Top
            };
            deleteButton.Click += DeleteRecord_Click;

            Grid.SetColumn(deleteButton, 1);
            grid.Children.Add(deleteButton);

            border.Child = grid;
            return border;
        }

        private async void DeleteRecord_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var record = button?.Tag as BloodPressureRecord;

            if (record == null) return;

            var result = MessageBox.Show(
                $"确定要删除 {record.RecordTime:HH:mm} 的血压记录吗？\n血压值：{record.SystolicPressure}/{record.DiastolicPressure} mmHg",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                var success = await _bloodPressureService.DeleteRecordAsync(record);
                if (success)
                {
                    await LoadTodayRecords();
                }
                else
                {
                    MessageBox.Show("删除记录失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BloodPressure_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateBloodPressureWarning();
        }

        private void UpdateBloodPressureWarning()
        {
            if (int.TryParse(SystolicTextBox.Text, out int systolic) &&
                int.TryParse(DiastolicTextBox.Text, out int diastolic))
            {
                if (systolic > 130 || diastolic > 90)
                {
                    BloodPressureWarning.Visibility = Visibility.Visible;
                    BloodPressureWarningText.Text = "血压偏高，建议咨询医生";
                }
                else if (systolic < 90 || diastolic < 60)
                {
                    BloodPressureWarning.Visibility = Visibility.Visible;
                    BloodPressureWarningText.Text = "血压偏低，请注意休息";
                }
                else
                {
                    BloodPressureWarning.Visibility = Visibility.Collapsed;
                }
            }
            else
            {
                BloodPressureWarning.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateStatusText()
        {
            // 状态信息现在显示在记录数量文本中，不需要单独的状态栏
        }

        private bool ValidateInput()
        {
            // 验证日期
            if (!DatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("请选择日期", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 验证收缩压
            if (!int.TryParse(SystolicTextBox.Text, out int systolic) || systolic < 50 || systolic > 250)
            {
                MessageBox.Show("请输入有效的收缩压值 (50-250)", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                SystolicTextBox.Focus();
                return false;
            }

            // 验证舒张压
            if (!int.TryParse(DiastolicTextBox.Text, out int diastolic) || diastolic < 30 || diastolic > 150)
            {
                MessageBox.Show("请输入有效的舒张压值 (30-150)", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                DiastolicTextBox.Focus();
                return false;
            }

            // 验证血压逻辑关系
            if (systolic <= diastolic)
            {
                MessageBox.Show("收缩压应该大于舒张压", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                SystolicTextBox.Focus();
                return false;
            }

            // 验证心率（如果输入了）
            if (!string.IsNullOrEmpty(HeartRateTextBox.Text))
            {
                if (!int.TryParse(HeartRateTextBox.Text, out int heartRate) || heartRate < 30 || heartRate > 200)
                {
                    MessageBox.Show("请输入有效的心率值 (30-200)", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    HeartRateTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                // 创建血压记录
                var record = new BloodPressureRecord
                {
                    Date = DatePicker.SelectedDate.Value,
                    RecordTime = DateTime.Now,
                    SystolicPressure = int.Parse(SystolicTextBox.Text),
                    DiastolicPressure = int.Parse(DiastolicTextBox.Text),
                    HeartRate = string.IsNullOrEmpty(HeartRateTextBox.Text) ? null : int.Parse(HeartRateTextBox.Text),
                    Notes = NotesTextBox.Text.Trim()
                };

                // 保存记录
                SaveButton.IsEnabled = false;
                SaveButton.Content = "保存中...";

                var success = await _bloodPressureService.AddBloodPressureRecordAsync(record);

                if (success)
                {
                    DataSaved = true;

                    // 清空输入框
                    SystolicTextBox.Clear();
                    DiastolicTextBox.Clear();
                    HeartRateTextBox.Clear();
                    NotesTextBox.Clear();
                    BloodPressureWarning.Visibility = Visibility.Collapsed;

                    // 重新加载记录
                    await LoadTodayRecords();
                }
                else
                {
                    MessageBox.Show("保存血压记录失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存过程中发生错误: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                SaveButton.Content = "保存记录";
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }
    }
}
