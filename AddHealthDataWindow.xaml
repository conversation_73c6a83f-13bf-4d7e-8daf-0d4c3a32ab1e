<Window x:Class="WpfAdmin.AddHealthDataWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="录入健康数据" Height="500" Width="600"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <!-- 现代化输入框样式 -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#5D6EFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3D4BD9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 取消按钮样式 -->
        <Style x:Key="CancelButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
            <Setter Property="Background" Value="#6C757D"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#5A6268"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#545B62"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 现代化日期选择器样式 -->
        <Style x:Key="ModernDatePicker" TargetType="DatePicker">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DatePicker">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 日期显示文本框 -->
                                <DatePickerTextBox x:Name="PART_TextBox"
                                                  Grid.Column="0"
                                                  Background="Transparent"
                                                  Foreground="White"
                                                  BorderThickness="0"
                                                  Padding="{TemplateBinding Padding}"
                                                  FontSize="{TemplateBinding FontSize}"
                                                  FontWeight="{TemplateBinding FontWeight}"
                                                  VerticalContentAlignment="Center"
                                                  IsReadOnly="True"/>

                                <!-- 日历按钮 -->
                                <Button x:Name="PART_Button"
                                       Grid.Column="1"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Width="30"
                                       Height="30"
                                       Margin="5,0"
                                       Cursor="Hand">
                                    <TextBlock Text="&#xE787;"
                                              FontFamily="Segoe MDL2 Assets"
                                              FontSize="16"
                                              Foreground="#A0A0FF"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                                CornerRadius="4">
                                                            <ContentPresenter HorizontalAlignment="Center"
                                                                            VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#4A5AE8"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                </Button>

                                <!-- 日历弹出窗口 -->
                                <Popup x:Name="PART_Popup"
                                      AllowsTransparency="True"
                                      Placement="Bottom"
                                      PlacementTarget="{Binding ElementName=PART_TextBox}"
                                      StaysOpen="False">
                                    <Border Background="#293153"
                                           CornerRadius="8"
                                           BorderThickness="1"
                                           BorderBrush="#5D6EFF"
                                           Margin="0,5,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect ShadowDepth="3" BlurRadius="10" Opacity="0.3"/>
                                        </Border.Effect>
                                        <Calendar x:Name="PART_Calendar"
                                                 Background="Transparent"
                                                 Foreground="White">
                                            <Calendar.Resources>
                                                <!-- 日历按钮样式 -->
                                                <Style TargetType="CalendarDayButton">
                                                    <Setter Property="Background" Value="Transparent"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="FontSize" Value="14"/>
                                                    <Setter Property="FontWeight" Value="Normal"/>
                                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="CalendarDayButton">
                                                                <Border Background="{TemplateBinding Background}"
                                                                        CornerRadius="4"
                                                                        Margin="1">
                                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                                    VerticalAlignment="Center"
                                                                                    TextElement.Foreground="{TemplateBinding Foreground}"/>
                                                                </Border>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="#5D6EFF"/>
                                                                    </Trigger>
                                                                    <Trigger Property="IsSelected" Value="True">
                                                                        <Setter Property="Background" Value="#5D6EFF"/>
                                                                        <Setter Property="Foreground" Value="White"/>
                                                                    </Trigger>
                                                                    <Trigger Property="IsToday" Value="True">
                                                                        <Setter Property="Background" Value="#FF725E"/>
                                                                        <Setter Property="Foreground" Value="White"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>

                                                <!-- 月份/年份按钮样式 -->
                                                <Style TargetType="CalendarButton">
                                                    <Setter Property="Background" Value="Transparent"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                    <Setter Property="FontSize" Value="14"/>
                                                    <Setter Property="FontWeight" Value="Medium"/>
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="CalendarButton">
                                                                <Border Background="{TemplateBinding Background}"
                                                                        CornerRadius="4"
                                                                        Margin="1">
                                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                                    VerticalAlignment="Center"
                                                                                    TextElement.Foreground="{TemplateBinding Foreground}"/>
                                                                </Border>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="#5D6EFF"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Calendar.Resources>
                                        </Calendar>
                                    </Border>
                                </Popup>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="#293153" CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="5" BlurRadius="15" Opacity="0.3" Color="Black"/>
        </Border.Effect>
        
        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="&#xE95E;" FontFamily="Segoe MDL2 Assets" 
                               FontSize="24" Foreground="#5D6EFF" 
                               VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock Text="录入健康数据" FontSize="24" 
                               Foreground="White" FontWeight="Medium" 
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <Button Grid.Column="1" x:Name="CloseButton" 
                        Width="30" Height="30" 
                        Background="Transparent" 
                        BorderThickness="0"
                        Click="CloseButton_Click"
                        Cursor="Hand">
                    <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets" 
                               FontSize="16" Foreground="#8890AD"/>
                </Button>
            </Grid>

            <!-- 表单内容 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- 日期选择 -->
                    <StackPanel>
                        <TextBlock Text="日期" Foreground="White" FontSize="14" 
                                   FontWeight="Medium" Margin="0,0,0,8"/>
                        <DatePicker x:Name="DatePicker"
                                   Style="{StaticResource ModernDatePicker}"
                                   SelectedDate="{x:Static sys:DateTime.Today}"
                                   SelectedDateChanged="DatePicker_SelectedDateChanged"
                                   xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
                    </StackPanel>

                    <!-- 步数输入 -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="今日步数" Foreground="White" FontSize="14"
                                   FontWeight="Medium" Margin="0,0,0,8"/>
                        <TextBox x:Name="StepsTextBox"
                                Style="{StaticResource ModernTextBox}"
                                Text="0"/>
                        <TextBlock Text="建议每日步数：8000-10000步"
                                   Foreground="#8890AD" FontSize="12"
                                   Margin="0,5,0,0"/>
                    </StackPanel>

                    <!-- 血压输入 -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="血压测量" Foreground="White" FontSize="14"
                                   FontWeight="Medium" Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="高压 (收缩压)" Foreground="#A0A0FF"
                                           FontSize="12" Margin="0,0,0,5"/>
                                <TextBox x:Name="SystolicTextBox"
                                        Style="{StaticResource ModernTextBox}"
                                        Text="120"/>
                            </StackPanel>

                            <TextBlock Grid.Column="1" Text="/"
                                      FontSize="24" Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Center"
                                      Margin="15,20,15,0"/>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="低压 (舒张压)" Foreground="#A0A0FF"
                                           FontSize="12" Margin="0,0,0,5"/>
                                <TextBox x:Name="DiastolicTextBox"
                                        Style="{StaticResource ModernTextBox}"
                                        Text="80"/>
                            </StackPanel>
                        </Grid>
                        <TextBlock Text="正常血压范围：高压 90-140 mmHg，低压 60-90 mmHg"
                                   Foreground="#8890AD" FontSize="12"
                                   Margin="0,8,0,0"/>
                        <TextBlock x:Name="BloodPressureWarning"
                                   Text="⚠️ 血压偏高，建议咨询医生"
                                   Foreground="#FF6B6B" FontSize="12"
                                   Margin="0,5,0,0"
                                   Visibility="Collapsed"/>
                    </StackPanel>

                    <!-- 备注输入 -->
                    <StackPanel>
                        <TextBlock Text="备注" Foreground="White" FontSize="14"
                                   FontWeight="Medium" Margin="0,0,0,8"/>
                        <TextBox x:Name="NotesTextBox"
                                Style="{StaticResource ModernTextBox}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                VerticalScrollBarVisibility="Auto"/>
                        <TextBlock Text="记录今日身体状况、运动情况等"
                                   Foreground="#8890AD" FontSize="12"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!-- 按钮区域 -->
            <Grid Grid.Row="2" Margin="0,30,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Button Grid.Column="1" x:Name="CancelButton" 
                        Content="取消" 
                        Style="{StaticResource CancelButton}"
                        Width="100" 
                        Margin="0,0,15,0"
                        Click="CancelButton_Click"/>
                
                <Button Grid.Column="2" x:Name="SaveButton" 
                        Content="保存" 
                        Style="{StaticResource ModernButton}"
                        Width="100"
                        Click="SaveButton_Click"/>
            </Grid>
        </Grid>
    </Border>
</Window>
