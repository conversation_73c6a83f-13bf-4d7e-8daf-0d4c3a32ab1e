using System;
using System.Windows;
using System.Windows.Input;
using WpfAdmin.Models;
using WpfAdmin.Services;

namespace WpfAdmin
{
    public partial class AddHealthDataWindow : Window
    {
        private readonly HealthService _healthService;
        public bool DataSaved { get; private set; } = false;

        public AddHealthDataWindow()
        {
            InitializeComponent();
            _healthService = new HealthService();
            
            // 设置默认日期为今天
            DatePicker.SelectedDate = DateTime.Today;
            
            // 绑定事件
            SystolicTextBox.TextChanged += BloodPressure_TextChanged;
            DiastolicTextBox.TextChanged += BloodPressure_TextChanged;
            
            // 加载今日已有数据（如果存在）
            LoadTodayData();
        }

        private async void LoadTodayData()
        {
            try
            {
                var todayData = await _healthService.GetTodayDataAsync();
                if (todayData != null)
                {
                    StepsTextBox.Text = todayData.Steps.ToString();
                    SystolicTextBox.Text = todayData.SystolicPressure.ToString();
                    DiastolicTextBox.Text = todayData.DiastolicPressure.ToString();
                    NotesTextBox.Text = todayData.Notes;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载今日数据失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void BloodPressure_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            CheckBloodPressure();
        }

        private void CheckBloodPressure()
        {
            try
            {
                if (int.TryParse(SystolicTextBox.Text, out int systolic) && 
                    int.TryParse(DiastolicTextBox.Text, out int diastolic))
                {
                    // 检查血压是否异常（高压>130 或 低压>90）
                    if (systolic > 130 || diastolic > 90)
                    {
                        BloodPressureWarning.Visibility = Visibility.Visible;
                        BloodPressureWarning.Text = "⚠️ 血压偏高，建议咨询医生";
                    }
                    else if (systolic < 90 || diastolic < 60)
                    {
                        BloodPressureWarning.Visibility = Visibility.Visible;
                        BloodPressureWarning.Text = "⚠️ 血压偏低，请注意身体状况";
                    }
                    else
                    {
                        BloodPressureWarning.Visibility = Visibility.Collapsed;
                    }
                }
                else
                {
                    BloodPressureWarning.Visibility = Visibility.Collapsed;
                }
            }
            catch
            {
                BloodPressureWarning.Visibility = Visibility.Collapsed;
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInput())
                {
                    return;
                }

                // 创建健康数据对象
                var healthData = new HealthData
                {
                    Date = DatePicker.SelectedDate ?? DateTime.Today,
                    Steps = int.Parse(StepsTextBox.Text),
                    SystolicPressure = int.Parse(SystolicTextBox.Text),
                    DiastolicPressure = int.Parse(DiastolicTextBox.Text),
                    Notes = NotesTextBox.Text.Trim()
                };

                // 保存数据
                SaveButton.IsEnabled = false;
                SaveButton.Content = "保存中...";

                bool success = await _healthService.UpdateHealthDataAsync(healthData);

                if (success)
                {
                    DataSaved = true;
                    MessageBox.Show("健康数据保存成功！", "成功", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("保存失败，请重试。", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存数据时发生错误: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                SaveButton.IsEnabled = true;
                SaveButton.Content = "保存";
            }
        }

        private bool ValidateInput()
        {
            // 验证步数
            if (!int.TryParse(StepsTextBox.Text, out int steps) || steps < 0)
            {
                MessageBox.Show("请输入有效的步数（大于等于0）", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                StepsTextBox.Focus();
                return false;
            }

            if (steps > 100000)
            {
                MessageBox.Show("步数不能超过100,000步", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                StepsTextBox.Focus();
                return false;
            }

            // 验证血压
            if (!int.TryParse(SystolicTextBox.Text, out int systolic) || systolic < 50 || systolic > 250)
            {
                MessageBox.Show("请输入有效的收缩压（50-250 mmHg）", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                SystolicTextBox.Focus();
                return false;
            }

            if (!int.TryParse(DiastolicTextBox.Text, out int diastolic) || diastolic < 30 || diastolic > 150)
            {
                MessageBox.Show("请输入有效的舒张压（30-150 mmHg）", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DiastolicTextBox.Focus();
                return false;
            }

            if (diastolic >= systolic)
            {
                MessageBox.Show("舒张压不能大于或等于收缩压", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DiastolicTextBox.Focus();
                return false;
            }

            // 验证日期
            if (!DatePicker.SelectedDate.HasValue)
            {
                MessageBox.Show("请选择日期", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DatePicker.Focus();
                return false;
            }

            if (DatePicker.SelectedDate.Value > DateTime.Today)
            {
                MessageBox.Show("不能选择未来的日期", "输入错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DatePicker.Focus();
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // 允许拖拽窗口
        protected override void OnMouseLeftButtonDown(MouseButtonEventArgs e)
        {
            base.OnMouseLeftButtonDown(e);
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        // 键盘快捷键
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            
            if (e.Key == Key.Escape)
            {
                CancelButton_Click(this, new RoutedEventArgs());
            }
            else if (e.Key == Key.Enter && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                SaveButton_Click(this, new RoutedEventArgs());
            }
        }
    }
}
