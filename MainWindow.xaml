<Window x:Class="WpfApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:WpfApp"
        xmlns:controls="clr-namespace:WpfApp.Controls"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        Title="Dashboard" Height="900" Width="1440"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="#364375"

        WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <!-- 图片URL转换器 -->
        <local:ImageUrlConverter x:Key="ImageUrlConverter"/>

        <!-- 聊天界面的转换器 -->
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <!-- 聊天消息背景颜色转换器 -->
        <local:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>

        <!-- 聊天消息头像背景颜色转换器 -->
        <local:BoolToAvatarBgConverter x:Key="BoolToAvatarBgConverter"/>

        <!-- 聊天消息头像图标转换器 -->
        <local:BoolToAvatarConverter x:Key="BoolToAvatarConverter"/>

        <!-- 反向布尔值到可见性的转换器 -->
        <local:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>

        <!-- 复制按钮样式 -->
        <Style x:Key="CopyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="Foreground" Value="#8890AD" />
            <Setter Property="Cursor" Value="Hand" />
            <Setter Property="Padding" Value="5,2" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3F4266" />
                                <Setter Property="Foreground" Value="White" />
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#353857" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 扁平化按钮样式 -->
        <Style x:Key="FlatButton" TargetType="Button">
            <Setter Property="Background" Value="#293153"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980b9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <!-- 扁平化按钮样式 -->
        <Style x:Key="ModernFlatButton" TargetType="Button">
            <Setter Property="Background" Value="#293153"/>
            <!-- 默认背景色 -->
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16 8"/>
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>

            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            RenderTransformOrigin="0.5,0.5">
                            <Grid>
                                <!-- 内容容器 -->
                                <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>

                                <!-- 点击涟漪效果 -->
                                <Border x:Name="RippleEffect"
                                   Opacity="0"
                                   Background="#40FFFFFF"
                                   CornerRadius="{Binding CornerRadius, ElementName=border}"/>
                            </Grid>
                            <Border.RenderTransform>
                                <ScaleTransform/>
                            </Border.RenderTransform>
                        </Border>

                        <ControlTemplate.Triggers>
                            <!-- 悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#1976D2"/>
                            </Trigger>

                            <!-- 按下效果 -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="RippleEffect" Property="Opacity" Value="0.4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#A0A0FF"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Left"
                                              VerticalAlignment="Center"
                                              Margin="15,0,0,0"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2D3555"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SidebarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#A0A0FF"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="0,5,0,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Left"
                                              VerticalAlignment="Center"
                                              Margin="20,0,0,0"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#2D3555"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="TimeframeButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#A0A0FF"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Width" Value="80"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ActiveTimeframeButtonStyle" TargetType="Button" BasedOn="{StaticResource TimeframeButtonStyle}">
            <Setter Property="Background" Value="#5D6EFF"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style x:Key="StatCardStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Margin" Value="10"/>
        </Style>
        <Style x:Key="DataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#364375"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
        </Style>

        <Style x:Key="DataGridRowStyle" TargetType="DataGridRow">
            <Setter Property="Background" Value="#293153"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="BorderBrush" Value="#364375"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#364375"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        <Style x:Key="MenuButton" TargetType="Button">
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 普通筛选按钮样式 -->
        <Style x:Key="OrderFilterButton" TargetType="Button">
            <Setter Property="Background" Value="#FF666666"/>
            <Setter Property="Foreground" Value="AntiqueWhite"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5,0"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="OrderAddButton" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid>
                            <!-- 按钮底部阴影，增强立体感 -->
                            <Ellipse Margin="0,2,0,0" Fill="#D9A000" />

                            <!-- 主按钮形状 -->
                            <Border x:Name="ButtonBorder"
                            CornerRadius="20"
                            Padding="15,8">
                                <Border.Background>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#FFDD33" Offset="0.0" />
                                        <GradientStop Color="#FFC200" Offset="1.0" />
                                    </LinearGradientBrush>
                                </Border.Background>

                                <!-- 内部细微渐变，增强立体感 -->
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="1" Direction="320"
                                              Color="#FFFFFF" Opacity="0.3"
                                              BlurRadius="2" />
                                </Border.Effect>

                                <!-- 按钮内容 -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Path x:Name="PlusIcon"
                                  Data="M0,8 L16,8 M8,0 L8,16"
                                  Stroke="White"
                                  StrokeThickness="2"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  Margin="0,0,5,0" />
                                    <TextBlock x:Name="ButtonText"
                                       Text="数据添加"
                                       Foreground="White"
                                       FontWeight="Bold"
                                       VerticalAlignment="Center" />
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- 触发器：鼠标悬停效果 -->
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#FFEA5A" Offset="0.0" />
                                            <GradientStop Color="#FFD233" Offset="1.0" />
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>

                            <!-- 触发器：按下效果 -->
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                            <GradientStop Color="#F0B300" Offset="0.0" />
                                            <GradientStop Color="#E0A000" Offset="1.0" />
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter TargetName="ButtonBorder" Property="Margin" Value="0,2,0,-2" />
                            </Trigger>

                            <!-- 禁用状态 -->
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.5" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>


        <!-- 状态按钮样式 -->
        <Style x:Key="StatusButton" TargetType="Button">
            <Setter Property="Background" Value="#FF4CD964"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#1A1A1A" Offset="0"/>
                        <GradientStop Color="#252525" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#333333"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="10" ShadowDepth="2" Color="#000000" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="20"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Text="✦"
                                         FontSize="11"
                                         Margin="15,0,5,0"
                                         VerticalAlignment="Center"
                                         Foreground="#FFFFFF"/>
                                <ContentPresenter Grid.Column="1"
                                                Margin="0,8,15,8"
                                                HorizontalAlignment="Left"
                                                VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#252525" Offset="0"/>
                                            <GradientStop Color="#2D2D2D" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="BorderBrush" Value="#444444"/>
                                <Setter Property="Cursor" Value="Hand"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 内联按钮样式 -->
        <Style x:Key="InlineButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#1A1A1A" Offset="0"/>
                        <GradientStop Color="#252525" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="Padding" Value="0,4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#333333"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect BlurRadius="8" ShadowDepth="1" Color="#000000" Opacity="0.25"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            TextBlock.FontWeight="Normal"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#252525" Offset="0"/>
                                            <GradientStop Color="#2D2D2D" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="BorderBrush" Value="#444444"/>
                                <Setter Property="Cursor" Value="Hand"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect BlurRadius="10" ShadowDepth="2" Color="#000000" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 详细按钮样式 -->
        <Style x:Key="DetailButton" TargetType="Button">
            <Setter Property="Background" Value="#5D6EFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 股票卡片样式 -->
        <Style x:Key="StockCardStyle" TargetType="Border">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Width" Value="220"/>
            <Setter Property="Height" Value="180"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 股票名称样式 -->
        <Style x:Key="StockNameStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 股票价格样式 -->
        <Style x:Key="StockPriceStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 股票涨跌幅样式 -->
        <Style x:Key="StockChangeStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 股票详情样式 -->
        <Style x:Key="StockDetailStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- 股票涨跌颜色 -->
        <SolidColorBrush x:Key="StockUpBrush" Color="#FF5252"/>
        <SolidColorBrush x:Key="StockDownBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="StockFlatBrush" Color="#607D8B"/>

        <!-- 图标按钮样式 -->
        <Style x:Key="IconButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3F4266"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#353857"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 表格编辑按钮样式 -->
        <Style x:Key="TableEditButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10 5"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>

            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            RenderTransformOrigin="0.5,0.5">
                            <Grid>
                                <!-- 内容容器 -->
                                <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                            </Grid>
                        </Border>

                        <ControlTemplate.Triggers>
                            <!-- 悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Background" Value="#364375"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Background="#293153" MouseDown="Border_Mousedown">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 标题文本 -->
                <TextBlock Text="健康管理系统" Foreground="White" FontSize="14"
                           VerticalAlignment="Center" Margin="15,0,0,0"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="MinimizeButton" Width="40" Height="40"
                            Background="Transparent" BorderThickness="0"
                            Click="MinimizeButton_Click">
                        <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets"
                                   FontSize="12" Foreground="White"/>
                    </Button>
                    <Button x:Name="MaximizeButton" Width="40" Height="40"
                            Background="Transparent" BorderThickness="0"
                            Click="MaximizeButton_Click">
                        <TextBlock Text="&#xE922;" FontFamily="Segoe MDL2 Assets"
                                   FontSize="12" Foreground="White"/>
                    </Button>
                    <Button x:Name="CloseButton" Width="40" Height="40"
                            Background="Transparent" BorderThickness="0"
                            Click="CloseButton_Click">
                        <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                   FontSize="12" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <!-- 左侧导航栏 -->
            <Border Background="#21284B" CornerRadius="25,0,0,25">
                <StackPanel>
                    <TextBlock Text="健康管理系统" FontSize="22" Foreground="White" HorizontalAlignment="Center" Margin="0,20,0,0"/>
                    <TextBlock Text="HealthTracker V1.0" FontSize="16" Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock Text="记录每一天的健康数据" FontSize="12" Foreground="#8890AD" HorizontalAlignment="Center" Margin="0,5,0,20"/>
                    <!-- Separator -->
                    <Border Grid.Row="0" Height="1" Background="#2D3555" VerticalAlignment="Bottom" Margin="20,0"/>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="HealthButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE95E;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="健康管理" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="NotesButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE789;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="随手记" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="MusicDataButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE9D9;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="音乐资料" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="ListingButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE8FD;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="工具集" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- 在左侧菜单的Messages按钮添加点击事件 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="DataAnalysisButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE8BD;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="智能体助手" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="CoffeeButton_Click">
                        <StackPanel Orientation="Horizontal">

                            <TextBlock Text="&#xEC32;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="生豆管理" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                    <!-- 咖啡分析按钮 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="CoffeeAnalysisButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xEC32;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="咖啡分析" FontSize="14" Margin="15,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="MusicButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE8B2;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="电影资料" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MenuButtonStyle}" Click="WeatherButton_Click" >
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE8A1;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="和风天气" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>





                    <!-- 将原来的Settings按钮替换为以下代码 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="SettingsButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE713;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="股票管理" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>



                    <!-- 添加新的Messages按钮 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="MessagesButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE715;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="影人名单" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>


                    <!-- 新片管理 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="FilmButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE715;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="新片管理" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>


                    <!-- 同样修改Logout按钮 -->
                    <Button Style="{StaticResource MenuButtonStyle}" Click="LogoutButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE7E8;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="退出系统" FontSize="14" Margin="15,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <!-- Movie按钮 -->
                </StackPanel>
            </Border>


            <!-- 主内容区域 - 仪表板 -->

            <Grid x:Name="DashboardContent" Grid.Column="1" Margin="20" Visibility="Visible" MouseDown="Border_Mousedown">

                <Grid.Background>
                    <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                        <GradientStop Color="#223266" Offset="0"/>
                        <GradientStop Color="#27396b" Offset="1"/>
                    </LinearGradientBrush>

                </Grid.Background>

                <Grid Grid.Column="1" Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <!-- Header -->
                    <Grid Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Text="健康管理 - 记录每一天的健康数据" FontSize="24" Foreground="White" FontWeight="Medium" VerticalAlignment="Center"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="AddHealthDataButton" Content="录入数据" Style="{StaticResource ModernFlatButton}" Click="AddHealthDataButton_Click" Margin="0,0,10,0"/>
                            <Button Content="本月" Style="{StaticResource ActiveTimeframeButtonStyle}" Margin="10,0"/>
                            <Button Content="本年" Style="{StaticResource TimeframeButtonStyle}" Margin="10,0,0,0"/>
                        </StackPanel>
                    </Grid>
                    <!-- Health Stats Cards -->
                    <Grid Grid.Row="1" Margin="0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <!-- Steps Card -->
                        <Border Grid.Column="0" Style="{StaticResource StatCardStyle}" Background="#D9E3FF">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Margin="20,0,0,0" VerticalAlignment="Center">
                                    <TextBlock Text="今日步数" Foreground="#5D6EFF" FontSize="14"/>
                                    <TextBlock x:Name="TodayStepsText" Text="0" Foreground="#5D6EFF" FontSize="32" FontWeight="Bold" Margin="0,5,0,0"/>
                                    <TextBlock Text="步" Foreground="#5D6EFF" FontSize="12"/>
                                </StackPanel>

                                <Ellipse Grid.Column="1" Width="80" Height="80" Fill="#E0E8FF" Margin="0,0,-20,0">
                                    <Ellipse.Effect>
                                        <BlurEffect Radius="10"/>
                                    </Ellipse.Effect>
                                </Ellipse>
                                <TextBlock Grid.Column="1" Text="&#xE805;" FontFamily="Segoe MDL2 Assets" FontSize="30" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,20,0"/>
                            </Grid>
                        </Border>
                        <!-- Blood Pressure Card -->
                        <Border Grid.Column="1" Style="{StaticResource StatCardStyle}" Background="#FFD9D1">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Margin="20,0,0,0" VerticalAlignment="Center">
                                    <TextBlock Text="最新血压" Foreground="#FF725E" FontSize="14"/>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock x:Name="LatestSystolicText" Text="120" Foreground="#FF725E" FontSize="24" FontWeight="Bold"/>
                                        <TextBlock Text="/" Foreground="#FF725E" FontSize="24" FontWeight="Bold" Margin="2,0"/>
                                        <TextBlock x:Name="LatestDiastolicText" Text="80" Foreground="#FF725E" FontSize="24" FontWeight="Bold"/>
                                    </StackPanel>
                                    <TextBlock Text="mmHg" Foreground="#FF725E" FontSize="12"/>
                                </StackPanel>

                                <Ellipse Grid.Column="1" Width="80" Height="80" Fill="#FFE8E0" Margin="0,0,-20,0">
                                    <Ellipse.Effect>
                                        <BlurEffect Radius="10"/>
                                    </Ellipse.Effect>
                                </Ellipse>
                                <TextBlock Grid.Column="1" Text="&#xE95E;" FontFamily="Segoe MDL2 Assets" FontSize="30" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,20,0"/>
                            </Grid>
                        </Border>
                        <!-- Monthly Summary Card -->
                        <Border Grid.Column="2" Style="{StaticResource StatCardStyle}" Background="#FFD1E8">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Margin="20,0,0,0" VerticalAlignment="Center">
                                    <TextBlock Text="本月总步数" Foreground="#FF5E8F" FontSize="14"/>
                                    <TextBlock x:Name="MonthlyStepsText" Text="0" Foreground="#FF5E8F" FontSize="32" FontWeight="Bold" Margin="0,5,0,0"/>
                                    <TextBlock Text="步" Foreground="#FF5E8F" FontSize="12"/>
                                </StackPanel>

                                <Ellipse Grid.Column="1" Width="80" Height="80" Fill="#FFE0F0" Margin="0,0,-20,0">
                                    <Ellipse.Effect>
                                        <BlurEffect Radius="10"/>
                                    </Ellipse.Effect>
                                </Ellipse>
                                <TextBlock Grid.Column="1" Text="&#xE8C1;" FontFamily="Segoe MDL2 Assets" FontSize="30" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,20,0"/>
                            </Grid>
                        </Border>
                    </Grid>
                    <!-- Charts and Lists -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 步数图表区域 -->
                        <Border Background="#293153" CornerRadius="15" Margin="0,0,10,0">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- 图表标题 -->
                                <StackPanel Orientation="Horizontal" Grid.Row="0">
                                    <Ellipse Width="16" Height="16" Fill="#5D6EFF" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="月度步数趋势" FontSize="18" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- 步数图表 -->
                                <lvc:CartesianChart Grid.Row="1" Margin="0,20,0,0" x:Name="StepsChart"
                                                   Background="Transparent"
                                                   Foreground="White">
                                    <lvc:CartesianChart.AxisX>
                                        <lvc:Axis Title="日期" Foreground="White">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="1" StrokeDashArray="2" Stroke="#404040"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisX>
                                    <lvc:CartesianChart.AxisY>
                                        <lvc:Axis Title="步数" Foreground="White">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="1" StrokeDashArray="2" Stroke="#404040"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisY>
                                </lvc:CartesianChart>
                            </Grid>
                        </Border>

                        <!-- 血压图表区域 -->
                        <Border Grid.Column="1" Background="#293153" CornerRadius="15" Margin="10,0,0,0">
                            <Grid Margin="20">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- 图表标题 -->
                                <StackPanel Orientation="Horizontal" Grid.Row="0">
                                    <Ellipse Width="16" Height="16" Fill="#FF725E" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="月度血压趋势" FontSize="18" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- 血压图表 -->
                                <lvc:CartesianChart Grid.Row="1" Margin="0,20,0,0" x:Name="BloodPressureChart"
                                                   Background="Transparent"
                                                   Foreground="White">
                                    <lvc:CartesianChart.AxisX>
                                        <lvc:Axis Title="日期" Foreground="White">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="1" StrokeDashArray="2" Stroke="#404040"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisX>
                                    <lvc:CartesianChart.AxisY>
                                        <lvc:Axis Title="血压值(mmHg)" Foreground="White">
                                            <lvc:Axis.Separator>
                                                <lvc:Separator StrokeThickness="1" StrokeDashArray="2" Stroke="#404040"/>
                                            </lvc:Axis.Separator>
                                        </lvc:Axis>
                                    </lvc:CartesianChart.AxisY>
                                </lvc:CartesianChart>
                            </Grid>
                        </Border>

                    </Grid>
                </Grid>
            </Grid>


            <!-- 电影内容区域 - 海报展示方式 -->
            <Grid x:Name="MovieContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <!-- 新增行用于分页控件 -->
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题 -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="电影资料库" FontSize="24" Foreground="White" FontWeight="Medium"/>
                        <TextBlock Text="(每页显示20条记录)" FontSize="14" Foreground="#8890AD" Margin="15,8,0,0"/>

                        <!-- 导出按钮 -->
                        <Button x:Name="ExportMoviesButton"
                                Content="导出数据"
                                Style="{StaticResource ModernFlatButton}"
                                Click="ExportMovies_Click"
                                Margin="15,0,0,0"/>
                    </StackPanel>

                    <!-- 搜索和筛选按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Border Background="#293153" CornerRadius="20" Width="200" Height="40" Margin="0,0,10,0">
                            <Grid>
                                <TextBox x:Name="MovieSearchTextBox" Background="Transparent" BorderThickness="0" Foreground="White"
                                         VerticalAlignment="Center" Margin="15,0,40,0"
                                         FontSize="14" KeyDown="MovieSearchTextBox_KeyDown"/>
                                <Button x:Name="SearchButton" HorizontalAlignment="Right" Width="40" Background="Transparent"
                                        BorderThickness="0" Cursor="Hand" Click="SearchButton_Click">
                                    <TextBlock Text="&#xE71E;" FontFamily="Segoe MDL2 Assets"
                                               FontSize="16" Foreground="White"/>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- 将筛选按钮改为下拉菜单样式的按钮 -->
                        <Grid>
                            <Button x:Name="FilterButton" Style="{StaticResource ModernFlatButton}" Click="FilterButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE71D;" FontFamily="Segoe MDL2 Assets"
                                               FontSize="16" VerticalAlignment="Center"/>
                                    <TextBlock Text="筛选" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>
                            <Popup x:Name="FilterOptionsPopup" IsOpen="False" PlacementTarget="{Binding ElementName=FilterButton}"
                                   Placement="Bottom" AllowsTransparency="True" StaysOpen="False">
                                <Border Background="#364375" CornerRadius="5" Padding="0" BorderThickness="1" BorderBrush="#4A5795">
                                    <StackPanel Width="150">
                                        <Button Content="评分" Style="{StaticResource PopupButtonStyle}" Click="RatingFilterButton_Click"/>
                                        <Separator Background="#4A5795" Height="1" Margin="5,0"/>
                                        <Button Content="时间" Style="{StaticResource PopupButtonStyle}" Click="TimeFilterButton_Click"/>
                                    </StackPanel>
                                </Border>
                            </Popup>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- 加载进度条 -->
                <Grid x:Name="MovieLoadingProgressGrid" Grid.Row="1" Margin="0,10,0,0" Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Text="正在加载电影数据..." HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                    <ProgressBar Grid.Row="1" x:Name="MovieLoadingProgressBar" Height="8" IsIndeterminate="True"
                                 Foreground="#5D6EFF" Background="#293153" BorderThickness="0"/>
                </Grid>

                <!-- 电影海报网格 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,20,0,10">
                    <ItemsControl x:Name="MovieItemsControl">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Width="200" Height="320" Margin="10" Background="#293153"
                                        CornerRadius="10" Cursor="Hand">
                                    <Border.Effect>
                                        <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                                    </Border.Effect>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- 海报图片区域 -->
                                        <Border Grid.Row="0" Margin="10,10,10,5" CornerRadius="8">
                                            <Border.Background>
                                                <ImageBrush ImageSource="{Binding PosterUrl, FallbackValue=/Images/default_poster.jpg}"
                                                            Stretch="UniformToFill"/>
                                            </Border.Background>

                                            <!-- 评分标签 -->
                                            <Border HorizontalAlignment="Right" VerticalAlignment="Top"
                                                    Background="#FFDD33" CornerRadius="0,8,0,8"
                                                    Padding="8,4" Margin="0">
                                                <TextBlock Text="{Binding Rating}" FontWeight="Bold"
                                                           Foreground="#333333"/>
                                            </Border>
                                        </Border>

                                        <!-- 电影信息 -->
                                        <StackPanel Grid.Row="1" Margin="10,0,10,10">
                                            <TextBlock Text="{Binding Title}" FontWeight="SemiBold"
                                                       Foreground="White" FontSize="14"
                                                       TextTrimming="CharacterEllipsis"/>
                                            <TextBlock Text="{Binding Director}" Foreground="#8890AD"
                                                       FontSize="12" Margin="0,3,0,0"
                                                       TextTrimming="CharacterEllipsis"/>
                                            <Grid Margin="0,5,0,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="{Binding Country}" Foreground="#A0A0FF"
                                                           FontSize="11"/>
                                                <TextBlock Grid.Column="1" Text="{Binding Genre}"
                                                           Foreground="#A0A0FF" FontSize="11"
                                                           TextTrimming="CharacterEllipsis"/>
                                            </Grid>
                                        </StackPanel>

                                        <!-- 悬停时显示的操作按钮 -->
                                        <Border Grid.RowSpan="2" Background="#80000000" Opacity="0"
                                                CornerRadius="10">
                                            <Border.Triggers>
                                                <EventTrigger RoutedEvent="Border.MouseEnter">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                                             To="1" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                                <EventTrigger RoutedEvent="Border.MouseLeave">
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                                             To="0" Duration="0:0:0.2"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </Border.Triggers>
                                            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                <!--<Button Content="查看详情" Style="{StaticResource FlatButton}"
                                                        Width="120" Margin="0,0,0,10"/>-->

                                                <Button Content="查看详情" Style="{StaticResource FlatButton}"
                                                        Width="120" Margin="0,0,0,10" Click="ViewMovieDetail_Click" Tag="{Binding}"/>
                                                <Button Content="编辑" Style="{StaticResource FlatButton}"
                                                        Width="120" Margin="0,0,0,10" Click="EditMovie_Click" Tag="{Binding}"/>
                                                <Button Content="删除" Style="{StaticResource FlatButton}"
                                                        Width="120" Background="#FF5252"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- 分页控件 -->
                <Border Grid.Row="2" Background="#293153" CornerRadius="10" Padding="15,10" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧信息 -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="显示" Foreground="#A0A0FF" VerticalAlignment="Center"/>
                            <ComboBox Width="60" Margin="5,0" SelectedIndex="0" Background="#364375" Foreground="White" BorderThickness="0">
                                <ComboBoxItem Content="18"/>
                                <ComboBoxItem Content="24"/>
                                <ComboBoxItem Content="36"/>
                                <ComboBoxItem Content="全部"/>
                            </ComboBox>
                            <TextBlock Text="条/页" Foreground="#A0A0FF" VerticalAlignment="Center"/>
                            <TextBlock Text="  共" Foreground="#A0A0FF" VerticalAlignment="Center" Margin="10,0,0,0"/>
                            <TextBlock x:Name="TotalMoviesText" Text="120" Foreground="White" VerticalAlignment="Center" FontWeight="SemiBold" Margin="5,0"/>
                            <TextBlock Text="条记录" Foreground="#A0A0FF" VerticalAlignment="Center"/>
                        </StackPanel>

                        <!-- 中间页码 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource FlatButton}" Width="40" Height="35" Margin="3,0" Click="PreviousPage_Click">
                                <TextBlock Text="&#xE72B;" FontFamily="Segoe MDL2 Assets" FontSize="14"/>
                            </Button>

                            <Button x:Name="PageButton1" Content="1" Style="{StaticResource FlatButton}" Width="35" Height="35" Margin="3,0" Background="#5D6EFF" Click="PageButton_Click" Tag="1"/>
                            <Button x:Name="PageButton2" Content="2" Style="{StaticResource FlatButton}" Width="35" Height="35" Margin="3,0" Click="PageButton_Click" Tag="2"/>
                            <Button x:Name="PageButton3" Content="3" Style="{StaticResource FlatButton}" Width="35" Height="35" Margin="3,0" Click="PageButton_Click" Tag="3"/>
                            <Button x:Name="PageButton4" Content="4" Style="{StaticResource FlatButton}" Width="35" Height="35" Margin="3,0" Click="PageButton_Click" Tag="4"/>
                            <Button x:Name="PageButton5" Content="5" Style="{StaticResource FlatButton}" Width="35" Height="35" Margin="3,0" Click="PageButton_Click" Tag="5"/>

                            <Button Style="{StaticResource FlatButton}" Width="40" Height="35" Margin="3,0" Click="NextPage_Click">
                                <TextBlock Text="&#xE72A;" FontFamily="Segoe MDL2 Assets" FontSize="14"/>
                            </Button>
                        </StackPanel>

                        <!-- 右侧跳转 -->
                        <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="跳至" Foreground="#A0A0FF" VerticalAlignment="Center"/>
                            <TextBox x:Name="PageJumpTextBox" Width="40" Margin="5,0" Background="#364375" Foreground="White" BorderThickness="0" TextAlignment="Center" VerticalContentAlignment="Center"/>
                            <TextBlock Text="页" Foreground="#A0A0FF" VerticalAlignment="Center"/>
                            <Button Content="确定" Style="{StaticResource FlatButton}" Width="50" Height="30" Margin="10,0,0,0" Click="JumpToPage_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>


            <!-- 股票管理内容区域 -->
            <Grid x:Name="StockContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/> <!-- 查询区域 -->
                    <RowDefinition Height="Auto"/> <!-- 自选股管理区域 -->
                </Grid.RowDefinitions>

                <!-- 股票面板标题 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Text="自选股票" FontSize="24" Foreground="White" FontWeight="Medium"/>

                    <Button x:Name="RefreshStocksButton" Content="刷新数据"
                            Style="{StaticResource ModernFlatButton}"
                            Click="RefreshStocks_Click"
                            Grid.Column="1"/>
                </Grid>

                <!-- 股票列表 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Margin="0,20,0,0">
                    <WrapPanel x:Name="StockItemsPanel" Orientation="Horizontal" HorizontalAlignment="Center"/>
                </ScrollViewer>

                <!-- 加载指示器 -->
                <Grid x:Name="StockLoadingPanel" Grid.RowSpan="2" Background="#80000000" Visibility="Collapsed">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock x:Name="StockLoadingText" Text="正在加载股票数据..." Foreground="White" FontSize="16" HorizontalAlignment="Center"/>
                        <ProgressBar Width="200" Height="10" Margin="0,10,0,0" IsIndeterminate="True"/>
                    </StackPanel>
                </Grid>

                <!-- 查询区域 -->
                <Border Grid.Row="2" Background="#293153" CornerRadius="15" Margin="0,20,0,0" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <Border Background="#364375" CornerRadius="10" Padding="15,10">
                            <Grid>
                                <TextBox x:Name="StockQueryTextBox"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Foreground="White"
                                        FontSize="14"
                                        VerticalAlignment="Center"
                                        GotFocus="StockQueryTextBox_GotFocus"
                                        LostFocus="StockQueryTextBox_LostFocus"/>
                                <TextBlock x:Name="StockQueryPlaceholder"
                                        Text="输入股票名称或代码..."
                                        Foreground="#8890AD"
                                        VerticalAlignment="Center"
                                        IsHitTestVisible="False"/>
                            </Grid>
                        </Border>

                        <Button Grid.Column="1"
                                Margin="10,0,0,0"
                                Width="120"
                                Height="45"
                                Background="#5D6EFF"
                                BorderThickness="0"
                                x:Name="QueryStockButton"
                                Click="QueryStockButton_Click"
                                Style="{StaticResource ModernFlatButton}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE71E;"
                                        FontFamily="Segoe MDL2 Assets"
                                        FontSize="16"
                                        VerticalAlignment="Center"/>
                                <TextBlock Text="查询股票"
                                        Margin="8,0,0,0"
                                        VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>

                <!-- 自选股管理区域 -->
                <Border Grid.Row="3" Background="#293153" CornerRadius="15" Margin="0,20,0,0" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                         <!--标题-->
                        <TextBlock Text="自选股管理" FontSize="18" Foreground="White" FontWeight="Medium" Margin="0,0,0,15"/>

                         <!--添加自选股表单-->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                             <!--股票名称输入-->
                            <Border Background="#364375" CornerRadius="10" Padding="15,10" Margin="0,0,10,0">
                                <Grid>
                                    <TextBox x:Name="StockNameTextBox"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Foreground="White"
                                            FontSize="14"
                                            VerticalAlignment="Center"
                                            GotFocus="StockNameTextBox_GotFocus"
                                            LostFocus="StockNameTextBox_LostFocus"/>
                                    <TextBlock x:Name="StockNamePlaceholder"
                                            Text="股票名称或代码"
                                            Foreground="#8890AD"
                                            VerticalAlignment="Center"
                                            IsHitTestVisible="False"/>
                                </Grid>
                            </Border>

                             <!--持有数量输入-->
                            <Border Grid.Column="1" Background="#364375" CornerRadius="10" Padding="15,10" Margin="10,0,10,0">
                                <Grid>
                                    <TextBox x:Name="StockSharesTextBox"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            Foreground="White"
                                            FontSize="14"
                                            VerticalAlignment="Center"
                                            GotFocus="StockSharesTextBox_GotFocus"
                                            LostFocus="StockSharesTextBox_LostFocus"/>
                                    <TextBlock x:Name="StockSharesPlaceholder"
                                            Text="持有数量"
                                            Foreground="#8890AD"
                                            VerticalAlignment="Center"
                                            IsHitTestVisible="False"/>
                                </Grid>
                            </Border>

                             <!--添加按钮-->
                            <Button Grid.Column="2"
                                    Width="120"
                                    Height="45"
                                    Background="#5D6EFF"
                                    BorderThickness="0"
                                    x:Name="AddStockButton"
                                    Click="AddStockButton_Click"
                                    Style="{StaticResource ModernFlatButton}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="&#xE710;"
                                            FontFamily="Segoe MDL2 Assets"
                                            FontSize="16"
                                            VerticalAlignment="Center"/>
                                    <TextBlock Text="添加自选股"
                                            Margin="8,0,0,0"
                                            VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                         <!--自选股列表-->
                        <ListView x:Name="CustomStockListView" Grid.Row="2" Margin="0,15,0,0"
                                  Background="Transparent" BorderThickness="0"
                                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                  ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#364375" CornerRadius="10" Padding="15,10" Margin="0,5" Width="450">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="{Binding Name}" Foreground="White" FontSize="14" FontWeight="SemiBold"/>
                                                <TextBlock Text="{Binding Code, StringFormat=({0})}" Foreground="#8890AD" FontSize="14" Margin="5,0,0,0"/>
                                                <TextBlock Text="{Binding Shares, StringFormat=持有: {0}股}" Foreground="#A0A0FF" FontSize="14" Margin="15,0,0,0"/>
                                            </StackPanel>

                                            <Button Grid.Column="1"
                                                    Margin="0,0,10,0"
                                                    Background="#4D5680"
                                                    BorderThickness="0"
                                                    Tag="{Binding Code}"
                                                    Click="EditStockButton_Click"
                                                    Style="{StaticResource ModernFlatButton}">
                                                <TextBlock Text="&#xE70F;"
                                                        FontFamily="Segoe MDL2 Assets"
                                                        FontSize="14"/>
                                            </Button>

                                            <Button Grid.Column="2"
                                                    Background="#E74C3C"
                                                    BorderThickness="0"
                                                    Tag="{Binding Code}"
                                                    Click="RemoveStockButton_Click"
                                                    Style="{StaticResource ModernFlatButton}">
                                                <TextBlock Text="&#xE74D;"
                                                        FontFamily="Segoe MDL2 Assets"
                                                        FontSize="14"/>
                                            </Button>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </Grid>
                </Border>
            </Grid>

            <!-- AI对话内容区域 -->
            <Grid x:Name="AIToolContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="AI助手" FontSize="24" Foreground="White" FontWeight="Medium"/>
                        <TextBlock Text="(DeepSeek R1)" FontSize="14" Foreground="#8890AD" Margin="15,8,0,0"/>
                    </StackPanel>

                    <!-- 设置按钮 -->
                    <Button Grid.Column="1" Style="{StaticResource ModernFlatButton}" >
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE713;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="设置" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- 聊天区域 -->
                <Border Grid.Row="1" Background="#293153" CornerRadius="15" Margin="0,20,0,20">
                    <ScrollViewer x:Name="ChatScrollViewer" VerticalScrollBarVisibility="Auto" Padding="20">
                        <ItemsControl x:Name="ChatMessagesControl">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,10"
                                            Background="{Binding IsUser, Converter={StaticResource BoolToBackgroundConverter}}"
                                            CornerRadius="10" Padding="15">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 头像 -->
                                            <Border Width="40" Height="40"
                                                    Background="{Binding IsUser, Converter={StaticResource BoolToAvatarBgConverter}}"
                                                    CornerRadius="20">
                                                <TextBlock Text="{Binding IsUser, Converter={StaticResource BoolToAvatarConverter}}"
                                                           FontFamily="Segoe MDL2 Assets"
                                                           FontSize="20" Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- 消息内容 -->
                                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                                <TextBlock Text="{Binding Sender}"
                                                           FontWeight="SemiBold"
                                                           Foreground="White"
                                                           Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding Content}"
                                                           Foreground="White"
                                                           TextWrapping="Wrap"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="{Binding Timestamp}"
                                                            Foreground="#8890AD"
                                                            FontSize="11"
                                                            Margin="0,5,0,0"
                                                            HorizontalAlignment="Left"/>
                                                    <!-- 复制按钮，仅当不是用户消息时显示 -->
                                                    <Button Grid.Column="1"
                                                            Content="复制"
                                                            Click="CopyVolcanoMessage_Click"
                                                            Tag="{Binding Content}"
                                                            Visibility="{Binding IsUser, Converter={StaticResource InverseBoolToVisibilityConverter}}"
                                                            Style="{StaticResource CopyButtonStyle}"
                                                            Foreground="#8890AD"
                                                            Background="Transparent"
                                                            BorderThickness="0"
                                                            FontSize="11"
                                                            Margin="0,5,0,0"
                                                            HorizontalAlignment="Right"
                                                            Cursor="Hand"/>
                                                </Grid>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Border>

                <!-- 输入区域 -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Border Background="#293153" CornerRadius="15" Padding="15">
                        <Grid>
                            <TextBox x:Name="MessageInputBox"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Foreground="White"
                                     FontSize="14"
                                     VerticalAlignment="Center"
                                     AcceptsReturn="True"
                                     TextWrapping="Wrap"
                                     MaxHeight="100"
                                     VerticalScrollBarVisibility="Auto"
                                     KeyDown="MessageInputBox_KeyDown"
                                     Padding="0,5"
                                     GotFocus="MessageInputBox_GotFocus"
                                     LostFocus="MessageInputBox_LostFocus"/>
                            <TextBlock x:Name="MessageInputPlaceholder"
                                       Text="输入您的问题..."
                                       Foreground="#8890AD"
                                       VerticalAlignment="Center"
                                       IsHitTestVisible="False"/>
                        </Grid>
                    </Border>

                    <Button Grid.Column="1"
                            Margin="10,0,0,0"
                            Width="60" Height="60"
                            Background="#5D6EFF"
                            BorderThickness="0"
                            x:Name="SendMessageButton"
                            Click="SendMessageButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="30">
                                    <TextBlock Text="&#xE724;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="20"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4D5EEF"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#3D4EDF"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Grid>

            <!-- 新片管理显示区域 -->
            <Grid x:Name="FilmContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <TextBlock Text="电影查询" FontSize="24" Foreground="White" FontWeight="Medium" Margin="0,0,0,20"/>

                <!-- 查询表单 -->
                <Grid Grid.Row="1" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Border Background="#293153" CornerRadius="8" Padding="15,10">
                        <TextBox x:Name="MovieQueryTextBox"
                                 FontSize="14"
                                 Foreground="White"
                                 Background="Transparent"
                                 BorderThickness="0"
                                 VerticalContentAlignment="Center"
                                 Padding="5,0"
                                 Tag="输入电影名称(中文)..."
                                 KeyDown="MovieQueryTextBox_KeyDown">
                         <TextBox.Style>
                             <Style TargetType="TextBox">
                                 <Setter Property="Template">
                                     <Setter.Value>
                                         <ControlTemplate TargetType="{x:Type TextBox}">
                                             <Grid>
                                                 <TextBox Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                          x:Name="textSource"
                                                          Background="Transparent"
                                                          BorderThickness="0"
                                                          Foreground="White"
                                                          CaretBrush="#4B91F1"
                                                          Panel.ZIndex="2" />
                                                 <TextBlock Text="{TemplateBinding Tag}"
                                                            Visibility="{Binding Path=Text.IsEmpty, RelativeSource={RelativeSource TemplatedParent}, Converter={StaticResource BoolToVisConverter}}"
                                                            Background="Transparent"
                                                            Padding="8,0,0,0"
                                                            VerticalAlignment="Center"
                                                            Foreground="#8890AD"/>
                                             </Grid>
                                         </ControlTemplate>
                                     </Setter.Value>
                                 </Setter>
                             </Style>
                         </TextBox.Style>
                         <TextBox.Effect>
                             <DropShadowEffect ShadowDepth="1" Direction="270" BlurRadius="5" Opacity="0.2" Color="Black"/>
                         </TextBox.Effect>
                     </TextBox></Border>

                    <Button x:Name="MovieQueryButton"
                            Grid.Column="1"
                            Content="查询"
                            Margin="10,0,0,0"
                            Padding="20,10"
                            Background="#4B91F1"
                            Foreground="White"
                            BorderThickness="0"
                            FontWeight="SemiBold"
                            Click="MovieQueryButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#4B91F1"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="8"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                <Border.Effect>
                                                    <DropShadowEffect BlurRadius="5" ShadowDepth="1" Opacity="0.3" Color="#000000"/>
                                                </Border.Effect>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#3A80E0"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#2A70D0"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter Property="Background" Value="#7A9AC0"/>
                                        <Setter Property="Opacity" Value="0.7"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>

                <!-- 加载指示器 -->
                <Grid x:Name="MovieQueryLoadingGrid" Grid.Row="2" Visibility="Collapsed">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="4" Background="Transparent" Foreground="#3498db"/>
                        <TextBlock Text="正在搜索..." Foreground="White" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- 结果显示区域 -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                    <Grid>
                        <!-- 电影列表容器 - 用于模糊查询结果 -->
                        <StackPanel x:Name="MovieSearchResultsPanel" Orientation="Vertical" Visibility="Collapsed">
                            <TextBlock x:Name="SearchResultsTitle" Text="搜索结果" Foreground="White" FontSize="18"
                                       FontWeight="SemiBold" Margin="0,0,0,15"/>
                            <ItemsControl x:Name="SearchResultsItemsControl">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#293153" CornerRadius="10" Margin="0,0,0,10" Padding="15">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 海报缩略图 -->
                                                <Border Width="80" Height="120" CornerRadius="6" Margin="0,0,15,0">
                                                    <Border.Background>
                                                        <ImageBrush ImageSource="{Binding PosterPath}" Stretch="UniformToFill"/>
                                                    </Border.Background>
                                                </Border>

                                                <!-- 基本信息 -->
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Title}" FontSize="16" Foreground="White"
                                                               FontWeight="SemiBold" Margin="0,0,0,5" TextWrapping="Wrap"/>
                                                    <TextBlock Text="{Binding Director}" Foreground="#A0A0FF"
                                                               FontSize="13" Margin="0,0,0,5"/>
                                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                                        <TextBlock Text="{Binding ReleaseDate}" Foreground="#A0D0FF"
                                                                   FontSize="12" Margin="0,0,15,0"/>
                                                        <TextBlock Text="{Binding VoteAverage, StringFormat={}{0:F1}分}"
                                                                   Foreground="#FFD700" FontSize="12"/>
                                                    </StackPanel>
                                                </StackPanel>

                                                <!-- 详情按钮 -->
                                                <Button Grid.Column="2" Content="查看详情" Background="#3498db"
                                                        Foreground="White" BorderThickness="0" Padding="10,5"
                                                        VerticalAlignment="Center" Tag="{Binding}"
                                                        Click="ViewMovieDetails_Click">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Background" Value="#3498db"/>
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border Background="{TemplateBinding Background}"
                                                                                CornerRadius="4"
                                                                                Padding="{TemplateBinding Padding}">
                                                                            <ContentPresenter HorizontalAlignment="Center"
                                                                                              VerticalAlignment="Center"/>
                                                                        </Border>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                            <Style.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="#2980b9"/>
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Button.Style>
                                                </Button>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>

                        <!-- 单个电影详情 -->
                        <Border x:Name="MovieQueryResultBorder" Background="#293153" CornerRadius="15" Padding="20" Visibility="Collapsed">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- 电影基本信息 -->
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- 海报 -->
                                    <Border Width="200" Height="300" CornerRadius="10" Margin="0,0,20,0">
                                        <Border.Background>
                                            <ImageBrush x:Name="MoviePosterImage" Stretch="UniformToFill"/>
                                        </Border.Background>
                                    </Border>

                                    <!-- 信息 -->
                                    <StackPanel Grid.Column="1">
                                        <TextBlock x:Name="MovieTitleText" Text="" FontSize="24" Foreground="White" FontWeight="Bold" Margin="0,0,0,10"/>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="导演:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieDirectorText" Grid.Column="1" Text="" Foreground="White" FontSize="14"/>
                                        </Grid>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="演员:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieCastText" Grid.Column="1" Text="" Foreground="White" FontSize="14" TextWrapping="Wrap"/>
                                        </Grid>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="类型:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieGenreText" Grid.Column="1" Text="" Foreground="White" FontSize="14"/>
                                        </Grid>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="国家:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieCountryText" Grid.Column="1" Text="" Foreground="White" FontSize="14"/>
                                        </Grid>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="上映日期:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieReleaseDateText" Grid.Column="1" Text="" Foreground="White" FontSize="14"/>
                                        </Grid>

                                        <Grid Margin="0,5,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="80"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Text="评分:" Foreground="#A0A0FF" FontSize="14"/>
                                            <TextBlock x:Name="MovieRatingText" Grid.Column="1" Text="" Foreground="White" FontSize="14"/>
                                        </Grid>
                                    </StackPanel>
                                </Grid>

                                <!-- 电影简介 -->
                                <StackPanel Grid.Row="1" Margin="0,20,0,0">
                                    <TextBlock Text="简介" Foreground="#A0A0FF" FontSize="16" Margin="0,0,0,10"/>
                                    <TextBlock x:Name="MovieOverviewText" Text="" Foreground="White" FontSize="14" TextWrapping="Wrap" LineHeight="24"/>

                                    <!-- 添加存储数据按钮 -->
                                    <Button x:Name="SaveToDbButton"
                                            Content="存储数据库"
                                            Margin="0,20,0,0"
                                            Padding="15,10"
                                            Background="#3498db"
                                            Foreground="White"
                                            BorderThickness="0"
                                            HorizontalAlignment="Left"
                                            Click="SaveToDbButton_Click">
                                        <Button.Style>
                                            <Style TargetType="Button">
                                                <Setter Property="Background" Value="#3498db"/>
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="Button">
                                                            <Border Background="{TemplateBinding Background}"
                                                                    CornerRadius="6"
                                                                    Padding="{TemplateBinding Padding}">
                                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                            </Border>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#2980b9"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </ScrollViewer>
            </Grid>

            <!-- 在原有Grid中添加Messages内容区域 -->
            <Grid x:Name="MessagesContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="Movie Directors" FontSize="24" Foreground="White" FontWeight="Medium"/>
                    </StackPanel>

                    <!-- 导出按钮 -->
                    <Button x:Name="ExportDirectorsButton"
                            Grid.Column="1"
                            Style="{StaticResource ModernFlatButton}"
                            Click="ExportDirectors_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE74E;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="导出CSV" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- 数据列表 -->
                <Border Grid.Row="1" Background="#293153" CornerRadius="15" Margin="0,20,0,0">
                    <Grid>
                        <DataGrid x:Name="DirectorsDataGrid"
                          Margin="20"
                          Background="#293153"
                          Foreground="White"
                          ColumnHeaderStyle="{StaticResource DataGridColumnHeaderStyle}"
                          RowStyle="{StaticResource DataGridRowStyle}"
                          AutoGenerateColumns="False">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Director Name (English)" Binding="{Binding DirectorEnglishName}" Width="*"/>
                                <DataGridTextColumn Header="Director Name (Chinese)" Binding="{Binding DirectorChineseName}" Width="*"/>
                                <DataGridTemplateColumn Header="资料" Width="80">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Content="查看"
                                                    Background="#3498db"
                                                    Foreground="White"
                                                    BorderThickness="0"
                                                    Padding="10,5"
                                                    Cursor="Hand"
                                                    Click="ViewDirectorInfo_Click"
                                                    Tag="{Binding}">
                                                <Button.Style>
                                                    <Style TargetType="Button">
                                                        <Setter Property="Background" Value="#3498db"/>
                                                        <Setter Property="Template">
                                                            <Setter.Value>
                                                                <ControlTemplate TargetType="Button">
                                                                    <Border Background="{TemplateBinding Background}"
                                                                            CornerRadius="4"
                                                                            Padding="{TemplateBinding Padding}">
                                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                                    </Border>
                                                                </ControlTemplate>
                                                            </Setter.Value>
                                                        </Setter>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#2980b9"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Button.Style>
                                            </Button>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>
            </Grid>
            <Grid x:Name="CoffeeContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <!-- 顶部按钮栏 -->
                <StackPanel Orientation="Horizontal" Margin="20,20,20,10">
                    <!-- 常规按钮样式 -->
                    <Button Style="{StaticResource ModernFlatButton}" Click="TypeButton_Click">
                        <StackPanel Orientation="Horizontal">

                            <TextBlock Text="&#xE789;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="状态" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernFlatButton}" Click="PriceButton_Click">
                        <StackPanel Orientation="Horizontal">

                            <TextBlock Text="&#xE8A1;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="价格" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernFlatButton}" Click="WeightButton_Click">
                        <StackPanel Orientation="Horizontal">

                            <TextBlock Text="&#xE75a;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="重量" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernFlatButton}" Click="CountryButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE909;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="国家" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernFlatButton}" Click="TimeButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE823;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="时间" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernFlatButton}" Click="QueryButton_Click">
                        <StackPanel Orientation="Horizontal">

                            <TextBlock Text="&#xE70F;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="查询" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>

                    <!-- 数据添加按钮 (特殊样式) -->
                    <Button Style="{StaticResource OrderAddButton}" Margin="5" Click="OrderAddButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE710;" FontFamily="Segoe MDL2 Assets" FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="数据添加" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- 数据列表 -->
                <DataGrid x:Name="CoffeeDataGrid"
                      Grid.Row="1"
                      Margin="0,0,20,20"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      Background="#293153"
                      Foreground="#A0A0FF"
                      BorderThickness="0"
                      RowBackground="#293153"
                      AlternatingRowBackground="#293153"
                      GridLinesVisibility="None"
                      HorizontalGridLinesBrush="Transparent"
                      VerticalGridLinesBrush="Transparent"
                      RowStyle="{StaticResource DataGridRowStyle}">
                    <DataGrid.Resources>
                        <Style TargetType="DataGridColumnHeader">
                            <Setter Property="Background" Value="#293153"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="Padding" Value="10,15"/>
                            <Setter Property="BorderThickness" Value="0"/>
                        </Style>
                        <Style TargetType="DataGridRow">
                            <Setter Property="Background" Value="#293153"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                        </Style>
                        <Style TargetType="DataGridCell">
                            <Setter Property="Background" Value="#293153"/>
                            <Setter Property="Foreground" Value="#A0A0FF"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Padding" Value="10,5"/>
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                            <Style.Triggers>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#364375"/>
                                    <Setter Property="BorderBrush" Value="Transparent"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                        <!-- 定义TextBlock样式，用于DataGridTextColumn单元格内容 -->
                        <Style x:Key="CenterAlignedText" TargetType="TextBlock">
                            <Setter Property="VerticalAlignment" Value="Center"/>
                            <Setter Property="HorizontalAlignment" Value="Left"/>
                        </Style>
                    </DataGrid.Resources>
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="名称" Binding="{Binding Name}" Width="*">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="类型" Binding="{Binding Type}" Width="50">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="价格" Binding="{Binding Price}" Width="50">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="重量" Binding="{Binding Weight}" Width="50">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="国家" Binding="{Binding Country}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="时间" Binding="{Binding Date}" Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource CenterAlignedText}"/>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTemplateColumn Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding StatusText}">
                                        <Button.Style>
                                            <Style TargetType="Button" BasedOn="{StaticResource InlineButtonStyle}">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="new">
                                                        <Setter Property="Background" Value="#FF4CD964"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="ing">
                                                        <Setter Property="Background" Value="#FFFFCC00"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="over">
                                                        <Setter Property="Background" Value="#FFFF3B30"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="road">
                                                        <Setter Property="Background" Value="Blue"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Button.Style>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="详细">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="详细" Style="{StaticResource InlineButtonStyle}" Click="ViewMemoButton_Click" Tag="{Binding}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="操作" Width="Auto">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource TableEditButton}" Click="EditCoffee_Click" Tag="{Binding}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="&#xE70F;" FontFamily="Segoe MDL2 Assets" FontSize="8" VerticalAlignment="Center"/>
                                            <TextBlock Text="编辑" FontSize="12" Margin="5,0,0,0"/>
                                        </StackPanel>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 添加加载进度条，覆盖在DataGrid上 -->
                <Grid Grid.Row="1" Background="#99293153" x:Name="CoffeeLoadingProgress" Visibility="Collapsed">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="正在加载数据..." Foreground="White" FontSize="16" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                        <ProgressBar IsIndeterminate="True" Width="200" Height="10" Foreground="#5D6EFF" Background="#454F7E"/>
                    </StackPanel>
                </Grid>
            </Grid>

            <!-- 火山方舟AI对话内容区域 -->
            <Grid x:Name="VolcanoContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="AI数据分析助手" FontSize="24" Foreground="White" FontWeight="Medium"/>
                        <TextBlock Text="(火山方舟)" FontSize="14" Foreground="#8890AD" Margin="15,8,0,0"/>
                    </StackPanel>

                    <!-- 设置按钮 -->
                    <Button Grid.Column="1" Style="{StaticResource ModernFlatButton}" >
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="&#xE713;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="16" VerticalAlignment="Center"/>
                            <TextBlock Text="设置" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- 进度条 -->
                <ProgressBar x:Name="VolcanoProgressBar" Grid.Row="1" IsIndeterminate="True"
                             Height="4" VerticalAlignment="Top" Margin="0,5,0,0"
                             Foreground="#FF5D6E" Background="Transparent"
                             BorderThickness="0" Visibility="Collapsed"/>

                <!-- 聊天区域 -->
                <Border Grid.Row="1" Background="#293153" CornerRadius="15" Margin="0,20,0,20">
                    <ScrollViewer x:Name="VolcanoScrollViewer" VerticalScrollBarVisibility="Auto" Padding="20">
                        <ItemsControl x:Name="VolcanoMessagesControl">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,10"
                                            Background="{Binding IsUser, Converter={StaticResource BoolToBackgroundConverter}}"
                                            CornerRadius="10" Padding="15">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 头像 -->
                                            <Border Width="40" Height="40"
                                                    Background="{Binding IsUser, Converter={StaticResource BoolToAvatarBgConverter}}"
                                                    CornerRadius="20">
                                                <TextBlock Text="{Binding IsUser, Converter={StaticResource BoolToAvatarConverter}}"
                                                           FontFamily="Segoe MDL2 Assets"
                                                           FontSize="20" Foreground="White"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"/>
                                            </Border>

                                            <!-- 消息内容 -->
                                            <StackPanel Grid.Column="1" Margin="15,0,0,0">
                                                <TextBlock Text="{Binding Sender}"
                                                           FontWeight="SemiBold"
                                                           Foreground="White"
                                                           Margin="0,0,0,5"/>
                                                <TextBlock Text="{Binding Content}"
                                                           Foreground="White"
                                                           TextWrapping="Wrap"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="{Binding Timestamp}"
                                                            Foreground="#8890AD"
                                                            FontSize="11"
                                                            Margin="0,5,0,0"
                                                            HorizontalAlignment="Left"/>
                                                    <!-- 复制按钮，仅当不是用户消息时显示 -->
                                                    <Button Grid.Column="1"
                                                            Content="复制"
                                                            Click="CopyVolcanoMessage_Click"
                                                            Tag="{Binding Content}"
                                                            Visibility="{Binding IsUser, Converter={StaticResource InverseBoolToVisibilityConverter}}"
                                                            Style="{StaticResource CopyButtonStyle}"
                                                            Foreground="#8890AD"
                                                            Background="Transparent"
                                                            BorderThickness="0"
                                                            FontSize="11"
                                                            Margin="0,5,0,0"
                                                            HorizontalAlignment="Right"
                                                            Cursor="Hand"/>
                                                </Grid>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Border>

                <!-- 输入区域 -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Border Background="#293153" CornerRadius="15" Padding="15">
                        <Grid>
                            <TextBox x:Name="VolcanoMessageInputBox"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Foreground="White"
                                     FontSize="14"
                                     VerticalAlignment="Center"
                                     AcceptsReturn="True"
                                     TextWrapping="Wrap"
                                     MaxHeight="100"
                                     VerticalScrollBarVisibility="Auto"
                                     KeyDown="VolcanoMessageInputBox_KeyDown"
                                     Padding="0,5"
                                     GotFocus="VolcanoMessageInputBox_GotFocus"
                                     LostFocus="VolcanoMessageInputBox_LostFocus"/>
                            <TextBlock x:Name="VolcanoMessageInputPlaceholder"
                                       Text="请输入您想要分析的数据..."
                                       Foreground="#8890AD"
                                       VerticalAlignment="Center"
                                       IsHitTestVisible="False"/>
                        </Grid>
                    </Border>

                    <Button Grid.Column="1"
                            Margin="10,0,0,0"
                            Width="60" Height="60"
                            Background="#FF5D6E"
                            BorderThickness="0"
                            x:Name="SendVolcanoMessageButton"
                            Click="SendVolcanoMessageButton_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="30">
                                    <TextBlock Text="&#xE724;"
                                               FontFamily="Segoe MDL2 Assets"
                                               FontSize="20"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#EF4D5E"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter Property="Background" Value="#DF3D4E"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>
                </Grid>
            </Grid>

            <!-- 咖啡数据分析内容区域 -->
            <Grid x:Name="CoffeeAnalysisContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <TextBlock Text="咖啡数据分析" FontSize="24" Foreground="White" FontWeight="Medium" Margin="0,0,0,20"/>

                <!-- 分析内容区域 -->
                <Grid Grid.Row="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 1. 按年度月份统计购买的咖啡费用 -->
                    <Border Grid.Row="0" Grid.Column="0" Background="#293153" CornerRadius="10" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TextBlock Text="月度消费统计" Foreground="#E0E0FF" FontSize="18" Margin="10,10,10,5" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                            <lvc:CartesianChart Grid.Row="1" Margin="10" x:Name="MonthlyExpenseChart" Series="{Binding MonthlyExpenseSeries}" LegendLocation="Bottom" Foreground="White">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </Border>

                    <!-- 2. 购买的国家地区信息统计 -->
                    <Border Grid.Row="0" Grid.Column="1" Background="#293153" CornerRadius="10" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TextBlock Text="产地分布" Foreground="#E0E0FF" FontSize="18" Margin="10,10,10,5" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                            <lvc:PieChart Grid.Row="1" Margin="10" x:Name="CountryDistributionChart" Series="{Binding CountryDistributionSeries}" LegendLocation="Bottom" Foreground="#E0E0FF" FontSize="12" FontWeight="Medium">
                                <lvc:PieChart.DataTooltip>
                                    <lvc:DefaultTooltip Background="#3D4266" Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:PieChart.DataTooltip>
                            </lvc:PieChart>
                        </Grid>
                    </Border>

                    <!-- 3. 咖啡生豆种类统计 -->
                    <Border Grid.Row="1" Grid.Column="0" Background="#293153" CornerRadius="10" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TextBlock Text="咖啡种类分布" Foreground="#E0E0FF" FontSize="18" Margin="10,10,10,5" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                            <lvc:PieChart Grid.Row="1" Margin="10" x:Name="CoffeeTypeChart" Series="{Binding CoffeeTypeSeries}" LegendLocation="Bottom" Foreground="#E0E0FF" FontSize="12" FontWeight="Medium">
                                <lvc:PieChart.DataTooltip>
                                    <lvc:DefaultTooltip Background="#3D4266" Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:PieChart.DataTooltip>
                            </lvc:PieChart>
                        </Grid>
                    </Border>

                    <!-- 4. 咖啡状态统计 -->
                    <Border Grid.Row="1" Grid.Column="1" Background="#293153" CornerRadius="10" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TextBlock Text="咖啡状态分布" Foreground="#E0E0FF" FontSize="18" Margin="10,10,10,5" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                            <lvc:CartesianChart Grid.Row="1" Margin="10" x:Name="CoffeeStatusChart" Series="{Binding CoffeeStatusSeries}" LegendLocation="Bottom" Foreground="White">
                                <lvc:CartesianChart.AxisX>
                                    <lvc:Axis Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:CartesianChart.AxisX>
                                <lvc:CartesianChart.AxisY>
                                    <lvc:Axis Foreground="#E0E0FF" FontSize="12" FontWeight="Medium"/>
                                </lvc:CartesianChart.AxisY>
                            </lvc:CartesianChart>
                        </Grid>
                    </Border>
                </Grid>

                <!-- 进度条覆盖层 -->
                <Grid Grid.Row="0" Grid.RowSpan="2" Background="#99293153" x:Name="AnalysisLoadingProgress" Visibility="Collapsed">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="正在分析数据..." Foreground="#E0E0FF" FontSize="18" Margin="0,0,0,10" HorizontalAlignment="Center" FontWeight="SemiBold"/>
                        <ProgressBar IsIndeterminate="True" Width="200" Height="10" Foreground="#5D6EFF" Background="#454F7E"/>
                    </StackPanel>
                </Grid>
            </Grid>

            <!-- 音乐内容区域 - 海报展示方式 -->
            <Grid x:Name="MusicContent" Grid.Column="1" Margin="20" Visibility="Collapsed">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题区域 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题 -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="音乐专辑库" FontSize="24" Foreground="White" FontWeight="Medium"/>
                        <TextBlock x:Name="MusicCountText" Text="(0条记录)" FontSize="14" Foreground="#8890AD" Margin="15,8,0,0"/>
                    </StackPanel>

                    <!-- 搜索和添加按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Border Background="#293153" CornerRadius="20" Width="200" Height="40" Margin="0,0,10,0">
                            <Grid>
                                <TextBox x:Name="MusicSearchTextBox" Background="Transparent" BorderThickness="0" Foreground="White"
                                         VerticalAlignment="Center" Margin="15,0,40,0"
                                         FontSize="14" KeyDown="MusicSearchTextBox_KeyDown"/>
                                <Button x:Name="MusicSearchButton" HorizontalAlignment="Right" Width="40" Background="Transparent"
                                        BorderThickness="0" Cursor="Hand" Click="MusicSearchButton_Click">
                                    <TextBlock Text="&#xE71E;" FontFamily="Segoe MDL2 Assets"
                                               FontSize="16" Foreground="White"/>
                                </Button>
                            </Grid>
                        </Border>

                        <!-- 添加音乐按钮 -->
                        <Button x:Name="AddMusicButton" Style="{StaticResource ModernFlatButton}" Click="AddMusicButton_Click" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE710;" FontFamily="Segoe MDL2 Assets"
                                           FontSize="16" VerticalAlignment="Center"/>
                                <TextBlock Text="添加专辑" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <!-- 图片下载按钮 -->
                        <Button x:Name="DownloadImagesButton" Style="{StaticResource ModernFlatButton}" Click="DownloadImagesButton_Click" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE118;" FontFamily="Segoe MDL2 Assets"
                                           FontSize="16" VerticalAlignment="Center"/>
                                <TextBlock Text="图片下载" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>

                        <!-- AI分析图片按钮 -->
                        <Button x:Name="AnalyzeImagesButton" Style="{StaticResource ModernFlatButton}" Click="AnalyzeImagesButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="&#xE7C1;" FontFamily="Segoe MDL2 Assets"
                                           FontSize="16" VerticalAlignment="Center"/>
                                <TextBlock Text="AI分析图片" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>

                <!-- 加载进度条 -->
                <Grid x:Name="MusicLoadingProgressGrid" Grid.Row="1" Margin="0,10,0,0" Visibility="Collapsed">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <TextBlock Text="正在加载音乐数据..." HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                    <ProgressBar Grid.Row="1" x:Name="MusicLoadingProgressBar" Height="8" IsIndeterminate="True"
                                 Foreground="#5D6EFF" Background="#293153" BorderThickness="0"/>
                </Grid>

                <!-- 音乐专辑网格 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,20,0,10">
                    <ItemsControl x:Name="MusicItemsControl">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Width="200" Height="320" Margin="10" Background="#293153"
                                        CornerRadius="10" Cursor="Hand">
                                    <Border.Effect>
                                        <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                                    </Border.Effect>
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- 专辑封面区域 -->
                                        <controls:MusicCoverControl Grid.Row="0" Margin="10,10,10,5"
                                                                   ImgLink="{Binding ImgLink}"
                                                                   CoverUrl="{Binding CoverUrl}"/>

                                        <!-- 专辑信息区域 -->
                                        <StackPanel Grid.Row="1" Margin="10,5,10,10">
                                            <TextBlock Text="{Binding Title}" FontWeight="SemiBold" Foreground="White"
                                                       TextTrimming="CharacterEllipsis" TextWrapping="NoWrap"/>
                                            <TextBlock Text="{Binding Artist}" Foreground="#8890AD" FontSize="12"
                                                       TextTrimming="CharacterEllipsis" TextWrapping="NoWrap"/>
                                            <Grid Margin="0,5,0,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="{Binding ReleaseDate}" Foreground="#8890AD" FontSize="11"/>
                                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                    <Button Style="{StaticResource IconButtonStyle}" ToolTip="编辑"
                                                            Tag="{Binding}" Click="EditMusic_Click">
                                                        <TextBlock Text="&#xE70F;" FontFamily="Segoe MDL2 Assets" FontSize="12"/>
                                                    </Button>
                                                    <Button Style="{StaticResource IconButtonStyle}" ToolTip="删除"
                                                            Tag="{Binding}" Click="DeleteMusic_Click" Margin="5,0,0,0">
                                                        <TextBlock Text="&#xE74D;" FontFamily="Segoe MDL2 Assets" FontSize="12"/>
                                                    </Button>
                                                </StackPanel>
                                            </Grid>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- 无数据提示 -->
                <TextBlock x:Name="NoMusicDataText" Grid.Row="1" Text="暂无音乐数据"
                           Foreground="White" FontSize="18" HorizontalAlignment="Center"
                           VerticalAlignment="Center" Visibility="Collapsed"/>
            </Grid>
        </Grid>
    </Grid>
</Window>