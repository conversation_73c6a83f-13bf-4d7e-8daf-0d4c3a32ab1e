using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using WpfAdmin.Models;

namespace WpfAdmin.Services
{
    /// <summary>
    /// 血压数据服务类
    /// </summary>
    public class BloodPressureService
    {
        private readonly string _csvFilePath;
        private readonly string _dataDirectory;

        public BloodPressureService()
        {
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "HealthData");
            _csvFilePath = Path.Combine(_dataDirectory, "blood_pressure_records.csv");
            
            // 确保目录存在
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }
            
            // 如果文件不存在，创建带标题行的CSV文件
            if (!File.Exists(_csvFilePath))
            {
                CreateCsvFile();
            }
        }

        private void CreateCsvFile()
        {
            var header = "Date,RecordTime,SystolicPressure,DiastolicPressure,HeartRate,Notes";
            File.WriteAllText(_csvFilePath, header + Environment.NewLine);
        }

        /// <summary>
        /// 添加血压记录
        /// </summary>
        public async Task<bool> AddBloodPressureRecordAsync(BloodPressureRecord record)
        {
            try
            {
                var heartRateValue = record.HeartRate?.ToString() ?? "";
                var csvLine = $"{record.Date:yyyy-MM-dd},{record.RecordTime:yyyy-MM-dd HH:mm:ss},{record.SystolicPressure},{record.DiastolicPressure},{heartRateValue},\"{record.Notes}\"";
                await File.AppendAllTextAsync(_csvFilePath, csvLine + Environment.NewLine);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"保存血压记录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有血压记录
        /// </summary>
        public async Task<List<BloodPressureRecord>> GetAllRecordsAsync()
        {
            try
            {
                if (!File.Exists(_csvFilePath))
                {
                    return new List<BloodPressureRecord>();
                }

                var lines = await File.ReadAllLinesAsync(_csvFilePath);
                var records = new List<BloodPressureRecord>();

                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = ParseCsvLine(line);
                    if (parts.Length >= 4)
                    {
                        var record = new BloodPressureRecord
                        {
                            Date = DateTime.ParseExact(parts[0], "yyyy-MM-dd", CultureInfo.InvariantCulture),
                            RecordTime = DateTime.ParseExact(parts[1], "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture),
                            SystolicPressure = int.Parse(parts[2]),
                            DiastolicPressure = int.Parse(parts[3]),
                            HeartRate = string.IsNullOrEmpty(parts[4]) ? null : int.Parse(parts[4]),
                            Notes = parts.Length > 5 ? parts[5].Trim('"') : string.Empty
                        };
                        records.Add(record);
                    }
                }

                return records.OrderByDescending(r => r.RecordTime).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取血压记录失败: {ex.Message}");
                return new List<BloodPressureRecord>();
            }
        }

        /// <summary>
        /// 获取指定日期的血压记录
        /// </summary>
        public async Task<List<BloodPressureRecord>> GetRecordsByDateAsync(DateTime date)
        {
            var allRecords = await GetAllRecordsAsync();
            return allRecords.Where(r => r.Date.Date == date.Date)
                           .OrderByDescending(r => r.RecordTime)
                           .ToList();
        }

        /// <summary>
        /// 获取今日血压记录
        /// </summary>
        public async Task<List<BloodPressureRecord>> GetTodayRecordsAsync()
        {
            return await GetRecordsByDateAsync(DateTime.Today);
        }

        /// <summary>
        /// 删除血压记录
        /// </summary>
        public async Task<bool> DeleteRecordAsync(BloodPressureRecord recordToDelete)
        {
            try
            {
                var allRecords = await GetAllRecordsAsync();
                var recordsToKeep = allRecords.Where(r => 
                    !(r.Date.Date == recordToDelete.Date.Date && 
                      r.RecordTime == recordToDelete.RecordTime &&
                      r.SystolicPressure == recordToDelete.SystolicPressure &&
                      r.DiastolicPressure == recordToDelete.DiastolicPressure)).ToList();

                await RewriteCsvFileAsync(recordsToKeep);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除血压记录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取指定日期的血压统计信息
        /// </summary>
        public async Task<BloodPressureStatistics> GetDailyStatisticsAsync(DateTime date)
        {
            var records = await GetRecordsByDateAsync(date);
            
            if (!records.Any())
            {
                return new BloodPressureStatistics();
            }

            return new BloodPressureStatistics
            {
                Date = date,
                RecordCount = records.Count,
                AverageSystolic = (int)records.Average(r => r.SystolicPressure),
                AverageDiastolic = (int)records.Average(r => r.DiastolicPressure),
                MaxSystolic = records.Max(r => r.SystolicPressure),
                MaxDiastolic = records.Max(r => r.DiastolicPressure),
                MinSystolic = records.Min(r => r.SystolicPressure),
                MinDiastolic = records.Min(r => r.DiastolicPressure),
                AbnormalCount = records.Count(r => r.IsAbnormal),
                LatestRecord = records.First() // 已按时间倒序排列
            };
        }

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            bool inQuotes = false;
            string currentField = "";

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(currentField);
                    currentField = "";
                }
                else
                {
                    currentField += c;
                }
            }

            result.Add(currentField);
            return result.ToArray();
        }

        private async Task RewriteCsvFileAsync(List<BloodPressureRecord> records)
        {
            var lines = new List<string>
            {
                "Date,RecordTime,SystolicPressure,DiastolicPressure,HeartRate,Notes"
            };

            foreach (var record in records)
            {
                var heartRateValue = record.HeartRate?.ToString() ?? "";
                var csvLine = $"{record.Date:yyyy-MM-dd},{record.RecordTime:yyyy-MM-dd HH:mm:ss},{record.SystolicPressure},{record.DiastolicPressure},{heartRateValue},\"{record.Notes}\"";
                lines.Add(csvLine);
            }

            await File.WriteAllLinesAsync(_csvFilePath, lines);
        }
    }

    /// <summary>
    /// 血压统计信息
    /// </summary>
    public class BloodPressureStatistics
    {
        public DateTime Date { get; set; }
        public int RecordCount { get; set; }
        public int AverageSystolic { get; set; }
        public int AverageDiastolic { get; set; }
        public int MaxSystolic { get; set; }
        public int MaxDiastolic { get; set; }
        public int MinSystolic { get; set; }
        public int MinDiastolic { get; set; }
        public int AbnormalCount { get; set; }
        public BloodPressureRecord? LatestRecord { get; set; }

        public string AverageBloodPressureText => $"{AverageSystolic}/{AverageDiastolic}";
        public string MaxBloodPressureText => $"{MaxSystolic}/{MaxDiastolic}";
        public string MinBloodPressureText => $"{MinSystolic}/{MinDiastolic}";
        public bool HasAbnormalRecords => AbnormalCount > 0;
    }
}
