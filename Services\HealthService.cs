using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using WpfAdmin.Models;

namespace WpfAdmin.Services
{
    public class HealthService
    {
        private readonly string _csvFilePath;
        private readonly string _dataDirectory;

        public HealthService()
        {
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "HealthData");
            _csvFilePath = Path.Combine(_dataDirectory, "health_data.csv");
            
            // 确保目录存在
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }
            
            // 如果文件不存在，创建带标题行的CSV文件
            if (!File.Exists(_csvFilePath))
            {
                CreateCsvFile();
            }
        }

        private void CreateCsvFile()
        {
            var header = "Date,Steps,SystolicPressure,DiastolicPressure,Notes";
            File.WriteAllText(_csvFilePath, header + Environment.NewLine);
        }

        public async Task<bool> AddHealthDataAsync(HealthData healthData)
        {
            try
            {
                var csvLine = $"{healthData.Date:yyyy-MM-dd},{healthData.Steps},{healthData.SystolicPressure},{healthData.DiastolicPressure},\"{healthData.Notes}\"";
                await File.AppendAllTextAsync(_csvFilePath, csvLine + Environment.NewLine);
                return true;
            }
            catch (Exception ex)
            {
                // 记录错误日志
                Console.WriteLine($"保存健康数据失败: {ex.Message}");
                return false;
            }
        }

        public async Task<List<HealthData>> GetHealthDataAsync()
        {
            try
            {
                if (!File.Exists(_csvFilePath))
                {
                    return new List<HealthData>();
                }

                var lines = await File.ReadAllLinesAsync(_csvFilePath);
                var healthDataList = new List<HealthData>();

                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i];
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = ParseCsvLine(line);
                    if (parts.Length >= 4)
                    {
                        var healthData = new HealthData
                        {
                            Date = DateTime.ParseExact(parts[0], "yyyy-MM-dd", CultureInfo.InvariantCulture),
                            Steps = int.Parse(parts[1]),
                            SystolicPressure = int.Parse(parts[2]),
                            DiastolicPressure = int.Parse(parts[3]),
                            Notes = parts.Length > 4 ? parts[4].Trim('"') : string.Empty
                        };
                        healthDataList.Add(healthData);
                    }
                }

                return healthDataList.OrderByDescending(h => h.Date).ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取健康数据失败: {ex.Message}");
                return new List<HealthData>();
            }
        }

        public async Task<List<HealthData>> GetMonthlyDataAsync(int year, int month)
        {
            var allData = await GetHealthDataAsync();
            return allData.Where(h => h.Date.Year == year && h.Date.Month == month).ToList();
        }

        public async Task<HealthData?> GetTodayDataAsync()
        {
            var allData = await GetHealthDataAsync();
            return allData.FirstOrDefault(h => h.Date.Date == DateTime.Today);
        }

        public async Task<HealthData?> GetLatestDataAsync()
        {
            var allData = await GetHealthDataAsync();
            return allData.FirstOrDefault();
        }

        public async Task<int> GetMonthlyStepsAsync(int year, int month)
        {
            var monthlyData = await GetMonthlyDataAsync(year, month);
            return monthlyData.Sum(h => h.Steps);
        }

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = "";
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current);
                    current = "";
                }
                else
                {
                    current += c;
                }
            }
            
            result.Add(current);
            return result.ToArray();
        }

        public async Task<bool> UpdateHealthDataAsync(HealthData healthData)
        {
            try
            {
                var allData = await GetHealthDataAsync();
                var existingData = allData.FirstOrDefault(h => h.Date.Date == healthData.Date.Date);
                
                if (existingData != null)
                {
                    // 更新现有数据
                    existingData.Steps = healthData.Steps;
                    existingData.SystolicPressure = healthData.SystolicPressure;
                    existingData.DiastolicPressure = healthData.DiastolicPressure;
                    existingData.Notes = healthData.Notes;
                    
                    // 重写整个文件
                    await RewriteCsvFileAsync(allData);
                    return true;
                }
                else
                {
                    // 添加新数据
                    return await AddHealthDataAsync(healthData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新健康数据失败: {ex.Message}");
                return false;
            }
        }

        private async Task RewriteCsvFileAsync(List<HealthData> healthDataList)
        {
            var lines = new List<string>
            {
                "Date,Steps,SystolicPressure,DiastolicPressure,Notes"
            };

            foreach (var data in healthDataList.OrderByDescending(h => h.Date))
            {
                var csvLine = $"{data.Date:yyyy-MM-dd},{data.Steps},{data.SystolicPressure},{data.DiastolicPressure},\"{data.Notes}\"";
                lines.Add(csvLine);
            }

            await File.WriteAllLinesAsync(_csvFilePath, lines);
        }

        public async Task<bool> DeleteHealthDataAsync(DateTime date)
        {
            try
            {
                var allData = await GetHealthDataAsync();
                var filteredData = allData.Where(h => h.Date.Date != date.Date).ToList();
                
                await RewriteCsvFileAsync(filteredData);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除健康数据失败: {ex.Message}");
                return false;
            }
        }

        public string GetDataFilePath()
        {
            return _csvFilePath;
        }
    }
}
