# 健康管理系统测试说明

## 功能概述

已成功实现健康管理系统，包含以下功能：

### 1. 首页健康管理界面
- 修改了原有的Dashboard页面为健康管理页面
- 显示今日步数、最新血压、本月总步数等统计信息
- 提供"录入数据"按钮用于添加健康数据

### 2. 健康数据录入窗口
- 现代化的UI设计，支持拖拽移动
- 日期选择（默认为今天）
- 步数录入（0-100,000步）
- 血压录入（收缩压和舒张压）
- 血压异常提醒（高压>130或低压>90时显示警告）
- 备注信息录入
- 数据验证和错误提示

### 3. 本地CSV数据存储
- 数据存储在用户文档目录下的HealthData文件夹
- 文件路径：`%USERPROFILE%\Documents\HealthData\health_data.csv`
- 支持数据的增加、更新、删除操作
- 不依赖Supabase数据库，完全本地存储

### 4. 图表展示功能
- **步数图表**：显示月度每日步数趋势
- **血压图表**：显示月度血压变化趋势
- 异常血压数据用不同颜色和形状标识（菱形）
- 正常血压数据用圆形标识

### 5. 数据统计
- 今日步数显示
- 最新血压值显示
- 本月累计步数统计

## 测试步骤

1. **启动程序**
   - 运行 `dotnet run` 启动应用程序
   - 点击左侧菜单的"健康管理"按钮

2. **录入测试数据**
   - 点击右上角"录入数据"按钮
   - 输入测试数据：
     - 步数：8000
     - 高压：120
     - 低压：80
     - 备注：正常运动日
   - 点击"保存"

3. **测试异常血压提醒**
   - 再次点击"录入数据"
   - 输入异常血压：
     - 高压：150
     - 低压：95
   - 观察是否显示血压异常警告

4. **查看图表**
   - 返回主界面查看步数和血压图表
   - 验证数据是否正确显示

5. **验证数据存储**
   - 检查文件：`%USERPROFILE%\Documents\HealthData\health_data.csv`
   - 确认数据已正确保存

## 技术实现

### 文件结构
- `Services/HealthService.cs` - 健康数据服务类
- `AddHealthDataWindow.xaml/.cs` - 数据录入窗口
- `Models/HealthData.cs` - 健康数据模型（已存在）
- `MainWindow.xaml/.cs` - 主窗口修改

### 关键特性
- 现代化UI设计
- 数据验证和错误处理
- 异常血压自动识别
- 本地CSV文件存储
- LiveCharts图表展示
- 响应式界面设计

## 注意事项

1. 程序首次运行时会自动创建健康数据目录和CSV文件
2. 血压异常标准：高压>130 或 低压>90
3. 所有数据均存储在本地，不涉及网络访问
4. 支持同一天数据的更新覆盖
5. 图表按月度显示，可扩展为其他时间周期

## 后续扩展建议

1. 添加体重、心率等其他健康指标
2. 支持数据导出功能
3. 添加健康报告生成
4. 实现数据备份和恢复
5. 添加健康目标设定和提醒功能
