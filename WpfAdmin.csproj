﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\MainWindow.xaml.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Markdig" Version="0.40.0" />
    <PackageReference Include="Microsoft.Azure.CognitiveServices.Vision.ComputerVision" Version="7.0.1" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2792.45" />
    <PackageReference Include="postgrest-csharp" Version="3.5.1" />
    <PackageReference Include="Supabase" Version="1.1.1" />
    <PackageReference Include="WindowsAPICodePack-Shell" Version="1.1.1" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="img\bj.png" />
    <Resource Include="img\head_bg.png" />
  </ItemGroup>

</Project>
