﻿#pragma checksum "..\..\..\AddBloodPressureWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "52A8B18866BB3D00BFC3EC25EBD23E198D8FD092"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfAdmin {
    
    
    /// <summary>
    /// AddBloodPressureWindow
    /// </summary>
    public partial class AddBloodPressureWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 227 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateDisplayText;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DatePicker;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SystolicTextBox;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiastolicTextBox;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HeartRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border BloodPressureWarning;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BloodPressureWarningText;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountText;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RecordsPanel;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\AddBloodPressureWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoRecordsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/addbloodpressurewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AddBloodPressureWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 206 "..\..\..\AddBloodPressureWindow.xaml"
            ((System.Windows.Controls.Grid)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DateDisplayText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 235 "..\..\..\AddBloodPressureWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.SystolicTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 276 "..\..\..\AddBloodPressureWindow.xaml"
            this.SystolicTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.BloodPressure_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DiastolicTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 287 "..\..\..\AddBloodPressureWindow.xaml"
            this.DiastolicTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.BloodPressure_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.HeartRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.BloodPressureWarning = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.BloodPressureWarningText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 335 "..\..\..\AddBloodPressureWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 338 "..\..\..\AddBloodPressureWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.RecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.RecordsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.NoRecordsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

