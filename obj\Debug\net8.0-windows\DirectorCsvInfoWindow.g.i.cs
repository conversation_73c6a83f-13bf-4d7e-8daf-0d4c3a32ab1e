﻿#pragma checksum "..\..\..\DirectorCsvInfoWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "54EFBD6CD2D3F1323065F2A1792510CFA44FAA9A"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WpfApp;


namespace WpfApp {
    
    
    /// <summary>
    /// DirectorCsvInfoWindow
    /// </summary>
    public partial class DirectorCsvInfoWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DirectorNameTitle;
        
        #line default
        #line hidden
        
        
        #line 26 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DirectorComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IdText;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EnglishNameText;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChineseNameText;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BirthdayText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PlaceOfBirthText;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BiographyText;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MoviesText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\DirectorCsvInfoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/directorcsvinfowindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\DirectorCsvInfoWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DirectorNameTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.DirectorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 28 "..\..\..\DirectorCsvInfoWindow.xaml"
            this.DirectorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DirectorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.IdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.EnglishNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ChineseNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BirthdayText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PlaceOfBirthText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.BiographyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.MoviesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.LoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

