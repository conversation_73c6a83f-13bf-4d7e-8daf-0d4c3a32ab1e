﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FD3AE30E40191DC396840AB1FCBCF4DB415EFCCF"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using Microsoft.Web.WebView2.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WpfApp;
using WpfApp.Controls;


namespace WpfApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 663 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 669 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 675 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 812 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 840 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddHealthDataButton;
        
        #line default
        #line hidden
        
        
        #line 841 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddBloodPressureButton;
        
        #line default
        #line hidden
        
        
        #line 863 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayStepsText;
        
        #line default
        #line hidden
        
        
        #line 886 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestSystolicText;
        
        #line default
        #line hidden
        
        
        #line 888 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestDiastolicText;
        
        #line default
        #line hidden
        
        
        #line 911 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyStepsText;
        
        #line default
        #line hidden
        
        
        #line 917 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyAverageStepsText;
        
        #line default
        #line hidden
        
        
        #line 953 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart StepsChart;
        
        #line default
        #line hidden
        
        
        #line 989 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart BloodPressureChart;
        
        #line default
        #line hidden
        
        
        #line 1035 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyTargetStepsText;
        
        #line default
        #line hidden
        
        
        #line 1049 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TargetProgressText;
        
        #line default
        #line hidden
        
        
        #line 1055 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProgressBarFill;
        
        #line default
        #line hidden
        
        
        #line 1064 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingDaysText;
        
        #line default
        #line hidden
        
        
        #line 1098 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BloodPressureStatsSubtitle;
        
        #line default
        #line hidden
        
        
        #line 1102 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBloodPressureButton;
        
        #line default
        #line hidden
        
        
        #line 1129 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpRecordCountText;
        
        #line default
        #line hidden
        
        
        #line 1139 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpAverageText;
        
        #line default
        #line hidden
        
        
        #line 1149 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpMaxText;
        
        #line default
        #line hidden
        
        
        #line 1159 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpAbnormalCountText;
        
        #line default
        #line hidden
        
        
        #line 1172 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieContent;
        
        #line default
        #line hidden
        
        
        #line 1193 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportMoviesButton;
        
        #line default
        #line hidden
        
        
        #line 1204 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 1207 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 1217 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterButton;
        
        #line default
        #line hidden
        
        
        #line 1224 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup FilterOptionsPopup;
        
        #line default
        #line hidden
        
        
        #line 1239 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 1245 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MovieLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 1251 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MovieItemsControl;
        
        #line default
        #line hidden
        
        
        #line 1367 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalMoviesText;
        
        #line default
        #line hidden
        
        
        #line 1377 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton1;
        
        #line default
        #line hidden
        
        
        #line 1378 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton2;
        
        #line default
        #line hidden
        
        
        #line 1379 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton3;
        
        #line default
        #line hidden
        
        
        #line 1380 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton4;
        
        #line default
        #line hidden
        
        
        #line 1381 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton5;
        
        #line default
        #line hidden
        
        
        #line 1391 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PageJumpTextBox;
        
        #line default
        #line hidden
        
        
        #line 1401 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockContent;
        
        #line default
        #line hidden
        
        
        #line 1418 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshStocksButton;
        
        #line default
        #line hidden
        
        
        #line 1426 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel StockItemsPanel;
        
        #line default
        #line hidden
        
        
        #line 1430 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 1432 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockLoadingText;
        
        #line default
        #line hidden
        
        
        #line 1447 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1455 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockQueryPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1469 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QueryStockButton;
        
        #line default
        #line hidden
        
        
        #line 1508 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 1516 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockNamePlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1527 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockSharesTextBox;
        
        #line default
        #line hidden
        
        
        #line 1535 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockSharesPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1549 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddStockButton;
        
        #line default
        #line hidden
        
        
        #line 1565 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView CustomStockListView;
        
        #line default
        #line hidden
        
        
        #line 1617 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AIToolContent;
        
        #line default
        #line hidden
        
        
        #line 1637 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectKmlButton;
        
        #line default
        #line hidden
        
        
        #line 1644 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearMapButton;
        
        #line default
        #line hidden
        
        
        #line 1673 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MapStatusText;
        
        #line default
        #line hidden
        
        
        #line 1677 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock KmlFileNameText;
        
        #line default
        #line hidden
        
        
        #line 1684 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Microsoft.Web.WebView2.Wpf.WebView2 MapWebView;
        
        #line default
        #line hidden
        
        
        #line 1695 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid FilmContent;
        
        #line default
        #line hidden
        
        
        #line 1713 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1752 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MovieQueryButton;
        
        #line default
        #line hidden
        
        
        #line 1797 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieQueryLoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 1808 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MovieSearchResultsPanel;
        
        #line default
        #line hidden
        
        
        #line 1809 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SearchResultsTitle;
        
        #line default
        #line hidden
        
        
        #line 1811 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl SearchResultsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 1879 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MovieQueryResultBorder;
        
        #line default
        #line hidden
        
        
        #line 1896 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ImageBrush MoviePosterImage;
        
        #line default
        #line hidden
        
        
        #line 1902 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieTitleText;
        
        #line default
        #line hidden
        
        
        #line 1910 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieDirectorText;
        
        #line default
        #line hidden
        
        
        #line 1919 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCastText;
        
        #line default
        #line hidden
        
        
        #line 1928 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieGenreText;
        
        #line default
        #line hidden
        
        
        #line 1937 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCountryText;
        
        #line default
        #line hidden
        
        
        #line 1946 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieReleaseDateText;
        
        #line default
        #line hidden
        
        
        #line 1955 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieRatingText;
        
        #line default
        #line hidden
        
        
        #line 1963 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieOverviewText;
        
        #line default
        #line hidden
        
        
        #line 1966 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveToDbButton;
        
        #line default
        #line hidden
        
        
        #line 2005 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MessagesContent;
        
        #line default
        #line hidden
        
        
        #line 2023 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDirectorsButton;
        
        #line default
        #line hidden
        
        
        #line 2038 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DirectorsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 2089 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeContent;
        
        #line default
        #line hidden
        
        
        #line 2153 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CoffeeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 2287 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2296 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid VolcanoContent;
        
        #line default
        #line hidden
        
        
        #line 2326 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar VolcanoProgressBar;
        
        #line default
        #line hidden
        
        
        #line 2333 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer VolcanoScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 2334 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl VolcanoMessagesControl;
        
        #line default
        #line hidden
        
        
        #line 2409 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VolcanoMessageInputBox;
        
        #line default
        #line hidden
        
        
        #line 2423 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VolcanoMessageInputPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 2436 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendVolcanoMessageButton;
        
        #line default
        #line hidden
        
        
        #line 2464 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeAnalysisContent;
        
        #line default
        #line hidden
        
        
        #line 2492 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart MonthlyExpenseChart;
        
        #line default
        #line hidden
        
        
        #line 2511 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CountryDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 2527 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CoffeeTypeChart;
        
        #line default
        #line hidden
        
        
        #line 2543 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart CoffeeStatusChart;
        
        #line default
        #line hidden
        
        
        #line 2556 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AnalysisLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2565 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicContent;
        
        #line default
        #line hidden
        
        
        #line 2582 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MusicCountText;
        
        #line default
        #line hidden
        
        
        #line 2589 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MusicSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 2592 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MusicSearchButton;
        
        #line default
        #line hidden
        
        
        #line 2601 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMusicButton;
        
        #line default
        #line hidden
        
        
        #line 2610 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2619 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2630 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 2636 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MusicLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 2642 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MusicItemsControl;
        
        #line default
        #line hidden
        
        
        #line 2698 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoMusicDataText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 650 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 665 "..\..\..\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 671 "..\..\..\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 677 "..\..\..\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 700 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.HealthButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 707 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NotesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 714 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 721 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ListingButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 729 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DataAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 736 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 744 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 751 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 758 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeatherButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 770 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 780 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 789 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FilmButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 798 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            
            #line 812 "..\..\..\MainWindow.xaml"
            this.DashboardContent.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AddHealthDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 840 "..\..\..\MainWindow.xaml"
            this.AddHealthDataButton.Click += new System.Windows.RoutedEventHandler(this.AddHealthDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.AddBloodPressureButton = ((System.Windows.Controls.Button)(target));
            
            #line 841 "..\..\..\MainWindow.xaml"
            this.AddBloodPressureButton.Click += new System.Windows.RoutedEventHandler(this.AddBloodPressureButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.TodayStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.LatestSystolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.LatestDiastolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.MonthlyStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.DailyAverageStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.StepsChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 27:
            this.BloodPressureChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 28:
            this.DailyTargetStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.TargetProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.ProgressBarFill = ((System.Windows.Controls.Border)(target));
            return;
            case 31:
            this.RemainingDaysText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.BloodPressureStatsSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.RefreshBloodPressureButton = ((System.Windows.Controls.Button)(target));
            
            #line 1105 "..\..\..\MainWindow.xaml"
            this.RefreshBloodPressureButton.Click += new System.Windows.RoutedEventHandler(this.RefreshBloodPressureButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.BpRecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.BpAverageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.BpMaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.BpAbnormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.MovieContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 39:
            this.ExportMoviesButton = ((System.Windows.Controls.Button)(target));
            
            #line 1196 "..\..\..\MainWindow.xaml"
            this.ExportMoviesButton.Click += new System.Windows.RoutedEventHandler(this.ExportMovies_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.MovieSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1206 "..\..\..\MainWindow.xaml"
            this.MovieSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 41:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 1208 "..\..\..\MainWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.FilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 1217 "..\..\..\MainWindow.xaml"
            this.FilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.FilterOptionsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 44:
            
            #line 1228 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RatingFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 1230 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.MovieLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 47:
            this.MovieLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 48:
            this.MovieItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 51:
            this.TotalMoviesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            
            #line 1373 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.PageButton1 = ((System.Windows.Controls.Button)(target));
            
            #line 1377 "..\..\..\MainWindow.xaml"
            this.PageButton1.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            this.PageButton2 = ((System.Windows.Controls.Button)(target));
            
            #line 1378 "..\..\..\MainWindow.xaml"
            this.PageButton2.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.PageButton3 = ((System.Windows.Controls.Button)(target));
            
            #line 1379 "..\..\..\MainWindow.xaml"
            this.PageButton3.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.PageButton4 = ((System.Windows.Controls.Button)(target));
            
            #line 1380 "..\..\..\MainWindow.xaml"
            this.PageButton4.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.PageButton5 = ((System.Windows.Controls.Button)(target));
            
            #line 1381 "..\..\..\MainWindow.xaml"
            this.PageButton5.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            
            #line 1383 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            case 59:
            this.PageJumpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 60:
            
            #line 1393 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.JumpToPage_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.StockContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 62:
            this.RefreshStocksButton = ((System.Windows.Controls.Button)(target));
            
            #line 1420 "..\..\..\MainWindow.xaml"
            this.RefreshStocksButton.Click += new System.Windows.RoutedEventHandler(this.RefreshStocks_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.StockItemsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 64:
            this.StockLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 65:
            this.StockLoadingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 66:
            this.StockQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1453 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1454 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 67:
            this.StockQueryPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 68:
            this.QueryStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1470 "..\..\..\MainWindow.xaml"
            this.QueryStockButton.Click += new System.Windows.RoutedEventHandler(this.QueryStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 69:
            this.StockNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1514 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1515 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 70:
            this.StockNamePlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 71:
            this.StockSharesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1533 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1534 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 72:
            this.StockSharesPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 73:
            this.AddStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1550 "..\..\..\MainWindow.xaml"
            this.AddStockButton.Click += new System.Windows.RoutedEventHandler(this.AddStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 74:
            this.CustomStockListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 77:
            this.AIToolContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 78:
            this.SelectKmlButton = ((System.Windows.Controls.Button)(target));
            
            #line 1637 "..\..\..\MainWindow.xaml"
            this.SelectKmlButton.Click += new System.Windows.RoutedEventHandler(this.SelectKmlButton_Click);
            
            #line default
            #line hidden
            return;
            case 79:
            this.ClearMapButton = ((System.Windows.Controls.Button)(target));
            
            #line 1644 "..\..\..\MainWindow.xaml"
            this.ClearMapButton.Click += new System.Windows.RoutedEventHandler(this.ClearMapButton_Click);
            
            #line default
            #line hidden
            return;
            case 80:
            this.MapStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 81:
            this.KmlFileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 82:
            this.MapWebView = ((Microsoft.Web.WebView2.Wpf.WebView2)(target));
            
            #line 1685 "..\..\..\MainWindow.xaml"
            this.MapWebView.NavigationCompleted += new System.EventHandler<Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs>(this.MapWebView_NavigationCompleted);
            
            #line default
            #line hidden
            
            #line 1686 "..\..\..\MainWindow.xaml"
            this.MapWebView.CoreWebView2InitializationCompleted += new System.EventHandler<Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs>(this.MapWebView_CoreWebView2InitializationCompleted);
            
            #line default
            #line hidden
            return;
            case 83:
            this.FilmContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 84:
            this.MovieQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1721 "..\..\..\MainWindow.xaml"
            this.MovieQueryTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieQueryTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 85:
            this.MovieQueryButton = ((System.Windows.Controls.Button)(target));
            
            #line 1761 "..\..\..\MainWindow.xaml"
            this.MovieQueryButton.Click += new System.Windows.RoutedEventHandler(this.MovieQueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 86:
            this.MovieQueryLoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 87:
            this.MovieSearchResultsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 88:
            this.SearchResultsTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 89:
            this.SearchResultsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 91:
            this.MovieQueryResultBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 92:
            this.MoviePosterImage = ((System.Windows.Media.ImageBrush)(target));
            return;
            case 93:
            this.MovieTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 94:
            this.MovieDirectorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 95:
            this.MovieCastText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 96:
            this.MovieGenreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 97:
            this.MovieCountryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 98:
            this.MovieReleaseDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 99:
            this.MovieRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 100:
            this.MovieOverviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 101:
            this.SaveToDbButton = ((System.Windows.Controls.Button)(target));
            
            #line 1974 "..\..\..\MainWindow.xaml"
            this.SaveToDbButton.Click += new System.Windows.RoutedEventHandler(this.SaveToDbButton_Click);
            
            #line default
            #line hidden
            return;
            case 102:
            this.MessagesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 103:
            this.ExportDirectorsButton = ((System.Windows.Controls.Button)(target));
            
            #line 2026 "..\..\..\MainWindow.xaml"
            this.ExportDirectorsButton.Click += new System.Windows.RoutedEventHandler(this.ExportDirectors_Click);
            
            #line default
            #line hidden
            return;
            case 104:
            this.DirectorsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 106:
            this.CoffeeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 107:
            
            #line 2097 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 108:
            
            #line 2105 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PriceButton_Click);
            
            #line default
            #line hidden
            return;
            case 109:
            
            #line 2113 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeightButton_Click);
            
            #line default
            #line hidden
            return;
            case 110:
            
            #line 2121 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CountryButton_Click);
            
            #line default
            #line hidden
            return;
            case 111:
            
            #line 2128 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeButton_Click);
            
            #line default
            #line hidden
            return;
            case 112:
            
            #line 2135 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 113:
            
            #line 2144 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OrderAddButton_Click);
            
            #line default
            #line hidden
            return;
            case 114:
            this.CoffeeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 117:
            this.CoffeeLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 118:
            this.VolcanoContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 119:
            this.VolcanoProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 120:
            this.VolcanoScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 121:
            this.VolcanoMessagesControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 123:
            this.VolcanoMessageInputBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2419 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.VolcanoMessageInputBox_KeyDown);
            
            #line default
            #line hidden
            
            #line 2421 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.GotFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 2422 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.LostFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 124:
            this.VolcanoMessageInputPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 125:
            this.SendVolcanoMessageButton = ((System.Windows.Controls.Button)(target));
            
            #line 2437 "..\..\..\MainWindow.xaml"
            this.SendVolcanoMessageButton.Click += new System.Windows.RoutedEventHandler(this.SendVolcanoMessageButton_Click);
            
            #line default
            #line hidden
            return;
            case 126:
            this.CoffeeAnalysisContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 127:
            this.MonthlyExpenseChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 128:
            this.CountryDistributionChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 129:
            this.CoffeeTypeChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 130:
            this.CoffeeStatusChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 131:
            this.AnalysisLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 132:
            this.MusicContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 133:
            this.MusicCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 134:
            this.MusicSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2591 "..\..\..\MainWindow.xaml"
            this.MusicSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MusicSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 135:
            this.MusicSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 2593 "..\..\..\MainWindow.xaml"
            this.MusicSearchButton.Click += new System.Windows.RoutedEventHandler(this.MusicSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 136:
            this.AddMusicButton = ((System.Windows.Controls.Button)(target));
            
            #line 2601 "..\..\..\MainWindow.xaml"
            this.AddMusicButton.Click += new System.Windows.RoutedEventHandler(this.AddMusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 137:
            this.DownloadImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2610 "..\..\..\MainWindow.xaml"
            this.DownloadImagesButton.Click += new System.Windows.RoutedEventHandler(this.DownloadImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 138:
            this.AnalyzeImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2619 "..\..\..\MainWindow.xaml"
            this.AnalyzeImagesButton.Click += new System.Windows.RoutedEventHandler(this.AnalyzeImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 139:
            this.MusicLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 140:
            this.MusicLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 141:
            this.MusicItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 144:
            this.NoMusicDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 49:
            
            #line 1333 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetail_Click);
            
            #line default
            #line hidden
            break;
            case 50:
            
            #line 1335 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMovie_Click);
            
            #line default
            #line hidden
            break;
            case 75:
            
            #line 1590 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 76:
            
            #line 1601 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 90:
            
            #line 1847 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetails_Click);
            
            #line default
            #line hidden
            break;
            case 105:
            
            #line 2057 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDirectorInfo_Click);
            
            #line default
            #line hidden
            break;
            case 115:
            
            #line 2266 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMemoButton_Click);
            
            #line default
            #line hidden
            break;
            case 116:
            
            #line 2274 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCoffee_Click);
            
            #line default
            #line hidden
            break;
            case 122:
            
            #line 2379 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyVolcanoMessage_Click);
            
            #line default
            #line hidden
            break;
            case 142:
            
            #line 2680 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMusic_Click);
            
            #line default
            #line hidden
            break;
            case 143:
            
            #line 2684 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteMusic_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

