﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "23A033A18F30F588B5D0F9459262D5D7078F64A3"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WpfApp;
using WpfApp.Controls;


namespace WpfApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 661 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 667 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 802 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 828 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddHealthDataButton;
        
        #line default
        #line hidden
        
        
        #line 850 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayStepsText;
        
        #line default
        #line hidden
        
        
        #line 873 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestSystolicText;
        
        #line default
        #line hidden
        
        
        #line 875 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestDiastolicText;
        
        #line default
        #line hidden
        
        
        #line 898 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyStepsText;
        
        #line default
        #line hidden
        
        
        #line 933 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart StepsChart;
        
        #line default
        #line hidden
        
        
        #line 969 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart BloodPressureChart;
        
        #line default
        #line hidden
        
        
        #line 996 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieContent;
        
        #line default
        #line hidden
        
        
        #line 1017 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportMoviesButton;
        
        #line default
        #line hidden
        
        
        #line 1028 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 1031 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 1041 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterButton;
        
        #line default
        #line hidden
        
        
        #line 1048 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup FilterOptionsPopup;
        
        #line default
        #line hidden
        
        
        #line 1063 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 1069 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MovieLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 1075 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MovieItemsControl;
        
        #line default
        #line hidden
        
        
        #line 1191 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalMoviesText;
        
        #line default
        #line hidden
        
        
        #line 1201 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton1;
        
        #line default
        #line hidden
        
        
        #line 1202 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton2;
        
        #line default
        #line hidden
        
        
        #line 1203 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton3;
        
        #line default
        #line hidden
        
        
        #line 1204 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton4;
        
        #line default
        #line hidden
        
        
        #line 1205 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton5;
        
        #line default
        #line hidden
        
        
        #line 1215 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PageJumpTextBox;
        
        #line default
        #line hidden
        
        
        #line 1225 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockContent;
        
        #line default
        #line hidden
        
        
        #line 1242 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshStocksButton;
        
        #line default
        #line hidden
        
        
        #line 1250 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel StockItemsPanel;
        
        #line default
        #line hidden
        
        
        #line 1254 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 1256 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockLoadingText;
        
        #line default
        #line hidden
        
        
        #line 1271 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1279 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockQueryPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1293 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QueryStockButton;
        
        #line default
        #line hidden
        
        
        #line 1332 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 1340 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockNamePlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1351 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockSharesTextBox;
        
        #line default
        #line hidden
        
        
        #line 1359 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockSharesPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1373 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddStockButton;
        
        #line default
        #line hidden
        
        
        #line 1389 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView CustomStockListView;
        
        #line default
        #line hidden
        
        
        #line 1441 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AIToolContent;
        
        #line default
        #line hidden
        
        
        #line 1472 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ChatScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 1473 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ChatMessagesControl;
        
        #line default
        #line hidden
        
        
        #line 1548 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageInputBox;
        
        #line default
        #line hidden
        
        
        #line 1562 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MessageInputPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1575 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendMessageButton;
        
        #line default
        #line hidden
        
        
        #line 1603 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid FilmContent;
        
        #line default
        #line hidden
        
        
        #line 1621 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1660 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MovieQueryButton;
        
        #line default
        #line hidden
        
        
        #line 1705 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieQueryLoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 1716 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MovieSearchResultsPanel;
        
        #line default
        #line hidden
        
        
        #line 1717 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SearchResultsTitle;
        
        #line default
        #line hidden
        
        
        #line 1719 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl SearchResultsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 1787 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MovieQueryResultBorder;
        
        #line default
        #line hidden
        
        
        #line 1804 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ImageBrush MoviePosterImage;
        
        #line default
        #line hidden
        
        
        #line 1810 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieTitleText;
        
        #line default
        #line hidden
        
        
        #line 1818 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieDirectorText;
        
        #line default
        #line hidden
        
        
        #line 1827 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCastText;
        
        #line default
        #line hidden
        
        
        #line 1836 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieGenreText;
        
        #line default
        #line hidden
        
        
        #line 1845 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCountryText;
        
        #line default
        #line hidden
        
        
        #line 1854 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieReleaseDateText;
        
        #line default
        #line hidden
        
        
        #line 1863 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieRatingText;
        
        #line default
        #line hidden
        
        
        #line 1871 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieOverviewText;
        
        #line default
        #line hidden
        
        
        #line 1874 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveToDbButton;
        
        #line default
        #line hidden
        
        
        #line 1913 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MessagesContent;
        
        #line default
        #line hidden
        
        
        #line 1931 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDirectorsButton;
        
        #line default
        #line hidden
        
        
        #line 1946 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DirectorsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 1997 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeContent;
        
        #line default
        #line hidden
        
        
        #line 2061 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CoffeeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 2195 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2204 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid VolcanoContent;
        
        #line default
        #line hidden
        
        
        #line 2234 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar VolcanoProgressBar;
        
        #line default
        #line hidden
        
        
        #line 2241 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer VolcanoScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 2242 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl VolcanoMessagesControl;
        
        #line default
        #line hidden
        
        
        #line 2317 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VolcanoMessageInputBox;
        
        #line default
        #line hidden
        
        
        #line 2331 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VolcanoMessageInputPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 2344 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendVolcanoMessageButton;
        
        #line default
        #line hidden
        
        
        #line 2372 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeAnalysisContent;
        
        #line default
        #line hidden
        
        
        #line 2400 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart MonthlyExpenseChart;
        
        #line default
        #line hidden
        
        
        #line 2419 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CountryDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 2435 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CoffeeTypeChart;
        
        #line default
        #line hidden
        
        
        #line 2451 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart CoffeeStatusChart;
        
        #line default
        #line hidden
        
        
        #line 2464 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AnalysisLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2473 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicContent;
        
        #line default
        #line hidden
        
        
        #line 2490 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MusicCountText;
        
        #line default
        #line hidden
        
        
        #line 2497 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MusicSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 2500 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MusicSearchButton;
        
        #line default
        #line hidden
        
        
        #line 2509 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMusicButton;
        
        #line default
        #line hidden
        
        
        #line 2518 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2527 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2538 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 2544 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MusicLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 2550 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MusicItemsControl;
        
        #line default
        #line hidden
        
        
        #line 2606 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoMusicDataText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 648 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 663 "..\..\..\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 669 "..\..\..\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 675 "..\..\..\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 698 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.HealthButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 704 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 711 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ListingButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 719 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DataAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 726 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 734 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 741 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 748 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeatherButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 760 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 770 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 779 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FilmButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 788 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            
            #line 802 "..\..\..\MainWindow.xaml"
            this.DashboardContent.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.AddHealthDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 828 "..\..\..\MainWindow.xaml"
            this.AddHealthDataButton.Click += new System.Windows.RoutedEventHandler(this.AddHealthDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.TodayStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.LatestSystolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.LatestDiastolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.MonthlyStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.StepsChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 24:
            this.BloodPressureChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 25:
            this.MovieContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 26:
            this.ExportMoviesButton = ((System.Windows.Controls.Button)(target));
            
            #line 1020 "..\..\..\MainWindow.xaml"
            this.ExportMoviesButton.Click += new System.Windows.RoutedEventHandler(this.ExportMovies_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.MovieSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1030 "..\..\..\MainWindow.xaml"
            this.MovieSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 28:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 1032 "..\..\..\MainWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.FilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 1041 "..\..\..\MainWindow.xaml"
            this.FilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.FilterOptionsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 31:
            
            #line 1052 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RatingFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 1054 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.MovieLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 34:
            this.MovieLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 35:
            this.MovieItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 38:
            this.TotalMoviesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            
            #line 1197 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.PageButton1 = ((System.Windows.Controls.Button)(target));
            
            #line 1201 "..\..\..\MainWindow.xaml"
            this.PageButton1.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.PageButton2 = ((System.Windows.Controls.Button)(target));
            
            #line 1202 "..\..\..\MainWindow.xaml"
            this.PageButton2.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.PageButton3 = ((System.Windows.Controls.Button)(target));
            
            #line 1203 "..\..\..\MainWindow.xaml"
            this.PageButton3.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.PageButton4 = ((System.Windows.Controls.Button)(target));
            
            #line 1204 "..\..\..\MainWindow.xaml"
            this.PageButton4.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.PageButton5 = ((System.Windows.Controls.Button)(target));
            
            #line 1205 "..\..\..\MainWindow.xaml"
            this.PageButton5.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 1207 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.PageJumpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 47:
            
            #line 1217 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.JumpToPage_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.StockContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 49:
            this.RefreshStocksButton = ((System.Windows.Controls.Button)(target));
            
            #line 1244 "..\..\..\MainWindow.xaml"
            this.RefreshStocksButton.Click += new System.Windows.RoutedEventHandler(this.RefreshStocks_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.StockItemsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 51:
            this.StockLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 52:
            this.StockLoadingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.StockQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1277 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1278 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 54:
            this.StockQueryPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 55:
            this.QueryStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1294 "..\..\..\MainWindow.xaml"
            this.QueryStockButton.Click += new System.Windows.RoutedEventHandler(this.QueryStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.StockNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1338 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1339 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 57:
            this.StockNamePlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.StockSharesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1357 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1358 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 59:
            this.StockSharesPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 60:
            this.AddStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1374 "..\..\..\MainWindow.xaml"
            this.AddStockButton.Click += new System.Windows.RoutedEventHandler(this.AddStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.CustomStockListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 64:
            this.AIToolContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 65:
            this.ChatScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 66:
            this.ChatMessagesControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 68:
            this.MessageInputBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1558 "..\..\..\MainWindow.xaml"
            this.MessageInputBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MessageInputBox_KeyDown);
            
            #line default
            #line hidden
            
            #line 1560 "..\..\..\MainWindow.xaml"
            this.MessageInputBox.GotFocus += new System.Windows.RoutedEventHandler(this.MessageInputBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1561 "..\..\..\MainWindow.xaml"
            this.MessageInputBox.LostFocus += new System.Windows.RoutedEventHandler(this.MessageInputBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 69:
            this.MessageInputPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 70:
            this.SendMessageButton = ((System.Windows.Controls.Button)(target));
            
            #line 1576 "..\..\..\MainWindow.xaml"
            this.SendMessageButton.Click += new System.Windows.RoutedEventHandler(this.SendMessageButton_Click);
            
            #line default
            #line hidden
            return;
            case 71:
            this.FilmContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 72:
            this.MovieQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1629 "..\..\..\MainWindow.xaml"
            this.MovieQueryTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieQueryTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 73:
            this.MovieQueryButton = ((System.Windows.Controls.Button)(target));
            
            #line 1669 "..\..\..\MainWindow.xaml"
            this.MovieQueryButton.Click += new System.Windows.RoutedEventHandler(this.MovieQueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 74:
            this.MovieQueryLoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 75:
            this.MovieSearchResultsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 76:
            this.SearchResultsTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 77:
            this.SearchResultsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 79:
            this.MovieQueryResultBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 80:
            this.MoviePosterImage = ((System.Windows.Media.ImageBrush)(target));
            return;
            case 81:
            this.MovieTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 82:
            this.MovieDirectorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 83:
            this.MovieCastText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 84:
            this.MovieGenreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 85:
            this.MovieCountryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 86:
            this.MovieReleaseDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 87:
            this.MovieRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 88:
            this.MovieOverviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 89:
            this.SaveToDbButton = ((System.Windows.Controls.Button)(target));
            
            #line 1882 "..\..\..\MainWindow.xaml"
            this.SaveToDbButton.Click += new System.Windows.RoutedEventHandler(this.SaveToDbButton_Click);
            
            #line default
            #line hidden
            return;
            case 90:
            this.MessagesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 91:
            this.ExportDirectorsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1934 "..\..\..\MainWindow.xaml"
            this.ExportDirectorsButton.Click += new System.Windows.RoutedEventHandler(this.ExportDirectors_Click);
            
            #line default
            #line hidden
            return;
            case 92:
            this.DirectorsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 94:
            this.CoffeeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 95:
            
            #line 2005 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 96:
            
            #line 2013 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PriceButton_Click);
            
            #line default
            #line hidden
            return;
            case 97:
            
            #line 2021 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeightButton_Click);
            
            #line default
            #line hidden
            return;
            case 98:
            
            #line 2029 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CountryButton_Click);
            
            #line default
            #line hidden
            return;
            case 99:
            
            #line 2036 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeButton_Click);
            
            #line default
            #line hidden
            return;
            case 100:
            
            #line 2043 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 101:
            
            #line 2052 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OrderAddButton_Click);
            
            #line default
            #line hidden
            return;
            case 102:
            this.CoffeeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 105:
            this.CoffeeLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 106:
            this.VolcanoContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 107:
            this.VolcanoProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 108:
            this.VolcanoScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 109:
            this.VolcanoMessagesControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 111:
            this.VolcanoMessageInputBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2327 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.VolcanoMessageInputBox_KeyDown);
            
            #line default
            #line hidden
            
            #line 2329 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.GotFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 2330 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.LostFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 112:
            this.VolcanoMessageInputPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 113:
            this.SendVolcanoMessageButton = ((System.Windows.Controls.Button)(target));
            
            #line 2345 "..\..\..\MainWindow.xaml"
            this.SendVolcanoMessageButton.Click += new System.Windows.RoutedEventHandler(this.SendVolcanoMessageButton_Click);
            
            #line default
            #line hidden
            return;
            case 114:
            this.CoffeeAnalysisContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 115:
            this.MonthlyExpenseChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 116:
            this.CountryDistributionChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 117:
            this.CoffeeTypeChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 118:
            this.CoffeeStatusChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 119:
            this.AnalysisLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 120:
            this.MusicContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 121:
            this.MusicCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 122:
            this.MusicSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2499 "..\..\..\MainWindow.xaml"
            this.MusicSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MusicSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 123:
            this.MusicSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 2501 "..\..\..\MainWindow.xaml"
            this.MusicSearchButton.Click += new System.Windows.RoutedEventHandler(this.MusicSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 124:
            this.AddMusicButton = ((System.Windows.Controls.Button)(target));
            
            #line 2509 "..\..\..\MainWindow.xaml"
            this.AddMusicButton.Click += new System.Windows.RoutedEventHandler(this.AddMusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 125:
            this.DownloadImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2518 "..\..\..\MainWindow.xaml"
            this.DownloadImagesButton.Click += new System.Windows.RoutedEventHandler(this.DownloadImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 126:
            this.AnalyzeImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2527 "..\..\..\MainWindow.xaml"
            this.AnalyzeImagesButton.Click += new System.Windows.RoutedEventHandler(this.AnalyzeImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 127:
            this.MusicLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 128:
            this.MusicLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 129:
            this.MusicItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 132:
            this.NoMusicDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 36:
            
            #line 1157 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetail_Click);
            
            #line default
            #line hidden
            break;
            case 37:
            
            #line 1159 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMovie_Click);
            
            #line default
            #line hidden
            break;
            case 62:
            
            #line 1414 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 63:
            
            #line 1425 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 67:
            
            #line 1518 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyVolcanoMessage_Click);
            
            #line default
            #line hidden
            break;
            case 78:
            
            #line 1755 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetails_Click);
            
            #line default
            #line hidden
            break;
            case 93:
            
            #line 1965 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDirectorInfo_Click);
            
            #line default
            #line hidden
            break;
            case 103:
            
            #line 2174 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMemoButton_Click);
            
            #line default
            #line hidden
            break;
            case 104:
            
            #line 2182 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCoffee_Click);
            
            #line default
            #line hidden
            break;
            case 110:
            
            #line 2287 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyVolcanoMessage_Click);
            
            #line default
            #line hidden
            break;
            case 130:
            
            #line 2588 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMusic_Click);
            
            #line default
            #line hidden
            break;
            case 131:
            
            #line 2592 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteMusic_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

