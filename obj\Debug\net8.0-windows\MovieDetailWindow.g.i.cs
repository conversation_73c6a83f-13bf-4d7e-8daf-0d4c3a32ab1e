﻿#pragma checksum "..\..\..\MovieDetailWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E894DCD7CA8CE4C9DC0C876EC79CF46048C18CFD"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfApp {
    
    
    /// <summary>
    /// MovieDetailWindow
    /// </summary>
    public partial class MovieDetailWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 101 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieTitle;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image PosterImage;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RatingText;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DirectorText;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountryText;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GenreText;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MagnetText;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DoubanText;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SublinkText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/moviedetailwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MovieDetailWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MovieTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PosterImage = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.RatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.DirectorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CountryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.GenreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.MagnetText = ((System.Windows.Controls.TextBox)(target));
            
            #line 149 "..\..\..\MovieDetailWindow.xaml"
            this.MagnetText.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.MagnetText_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 151 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyMagnet_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DoubanText = ((System.Windows.Controls.TextBox)(target));
            
            #line 162 "..\..\..\MovieDetailWindow.xaml"
            this.DoubanText.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.DoubanText_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 164 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyDouban_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SublinkText = ((System.Windows.Controls.TextBox)(target));
            
            #line 175 "..\..\..\MovieDetailWindow.xaml"
            this.SublinkText.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SublinkText_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 177 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopySublink_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 185 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

