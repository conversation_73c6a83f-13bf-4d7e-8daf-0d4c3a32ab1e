﻿#pragma checksum "..\..\..\NotesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "35C9820CCF9F0981BDC975114D891F71388E8223"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfApp {
    
    
    /// <summary>
    /// NotesWindow
    /// </summary>
    public partial class NotesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 194 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchBox;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView NotesListView;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox HeadingComboBox;
        
        #line default
        #line hidden
        
        
        #line 443 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HtmlModeToggle;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HtmlModeText;
        
        #line default
        #line hidden
        
        
        #line 455 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton PreviewToggle;
        
        #line default
        #line hidden
        
        
        #line 471 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MarkdownEditor;
        
        #line default
        #line hidden
        
        
        #line 482 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WebBrowser MarkdownPreview;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WordCountText;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\NotesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SaveStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/noteswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\NotesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 181 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_MouseDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\NotesWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\NotesWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\NotesWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.NotesListView = ((System.Windows.Controls.ListView)(target));
            
            #line 273 "..\..\..\NotesWindow.xaml"
            this.NotesListView.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NotesListView_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 329 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewNote_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.HeadingComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 372 "..\..\..\NotesWindow.xaml"
            this.HeadingComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.HeadingComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 384 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Bold_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 388 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Italic_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 392 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Underline_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 396 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Strikethrough_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 405 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BulletList_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 409 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NumberedList_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 413 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TaskList_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 422 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertLink_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 426 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertImage_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 430 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertCode_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 434 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InsertTable_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.HtmlModeToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 445 "..\..\..\NotesWindow.xaml"
            this.HtmlModeToggle.Click += new System.Windows.RoutedEventHandler(this.ToggleHtmlMode_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.HtmlModeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.PreviewToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 457 "..\..\..\NotesWindow.xaml"
            this.PreviewToggle.Checked += new System.Windows.RoutedEventHandler(this.PreviewToggle_Checked);
            
            #line default
            #line hidden
            
            #line 457 "..\..\..\NotesWindow.xaml"
            this.PreviewToggle.Unchecked += new System.Windows.RoutedEventHandler(this.PreviewToggle_Unchecked);
            
            #line default
            #line hidden
            return;
            case 23:
            this.MarkdownEditor = ((System.Windows.Controls.TextBox)(target));
            
            #line 479 "..\..\..\NotesWindow.xaml"
            this.MarkdownEditor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MarkdownEditor_TextChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.MarkdownPreview = ((System.Windows.Controls.WebBrowser)(target));
            
            #line 484 "..\..\..\NotesWindow.xaml"
            this.MarkdownPreview.Loaded += new System.Windows.RoutedEventHandler(this.MarkdownPreview_Loaded);
            
            #line default
            #line hidden
            return;
            case 25:
            this.WordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.SaveStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            
            #line 506 "..\..\..\NotesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveNote_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

