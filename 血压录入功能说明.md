# 血压录入功能使用说明

## 功能概述

已成功在健康管理页面添加了血压录入功能，支持单日多次血压数据录入，并提供详细的统计显示。

## 新增功能

### 1. 血压录入按钮
- 位置：健康管理页面右上角，"录入数据"按钮旁边
- 按钮名称：**"录入血压"**
- 功能：打开专门的血压录入窗口

### 2. 血压录入窗口
- **统一UI风格**：与健康数据录入窗口保持一致的深色主题设计
- **现代化界面**：深蓝色背景(#293153)，支持拖拽移动
- **清晰的输入框**：深色输入框(#364375)，白色文字，易于查看输入内容
- **日期选择**：可选择任意日期进行血压录入，默认选择今天
- **血压输入**：
  - 收缩压（高压）：50-250 mmHg
  - 舒张压（低压）：30-150 mmHg
  - 自动验证血压逻辑关系（收缩压必须大于舒张压）
  - 清晰的标签和提示信息
- **心率输入**：可选项，30-200次/分
- **备注信息**：支持多行文本输入
- **实时血压状态提醒**：
  - 血压偏高：收缩压>130或舒张压>90
  - 血压偏低：收缩压<90或舒张压<60
  - 橙红色警告背景，白色文字
- **单日多次录入**：支持同一天录入多次血压数据
- **自动时间记录**：每次录入自动记录精确时间（到分钟）

### 3. 今日血压记录显示
- **实时记录列表**：显示当日所有血压记录，与主窗口风格一致
- **深色主题设计**：记录项使用深色背景(#364375)，白色文字
- **详细信息显示**：
  - 录入时间（HH:mm格式）
  - 血压值（收缩压/舒张压）
  - 血压等级（正常、正常偏高、轻度高血压等）
  - 心率（如果有录入）
  - 备注信息
- **智能颜色标识**：
  - 绿色：正常血压值
  - 红色：异常血压值
  - 鼠标悬停时背景变亮，提供视觉反馈
- **删除功能**：每条记录右侧有删除按钮，可删除错误的血压记录
- **滚动支持**：记录列表支持垂直滚动，适应多条记录显示

### 4. 单日血压统计区域
位置：健康管理页面下方，新增的统计区域

#### 统计卡片包括：
1. **记录次数**：显示当日血压录入次数
2. **平均血压**：当日所有记录的平均值
3. **最高血压**：当日最高血压值
4. **异常次数**：血压异常的记录次数

#### 智能状态显示：
- **有异常记录时**：
  - 副标题显示红色警告："已录入X次，X次异常"
  - 平均血压显示黄色警告色
- **无异常记录时**：
  - 副标题显示绿色正常："已录入X次，血压正常"
  - 平均血压显示绿色正常色
- **无数据时**：显示"今日暂无血压数据"

### 5. 数据存储
- **存储位置**：`%USERPROFILE%\Documents\HealthData\blood_pressure_records.csv`
- **存储格式**：CSV文件，包含日期、时间、血压值、心率、备注
- **数据安全**：完全本地存储，不依赖网络

## 血压等级标准

| 血压等级 | 收缩压(mmHg) | 舒张压(mmHg) | 颜色标识 |
|---------|-------------|-------------|----------|
| 偏低 | <90 | <60 | 蓝色 |
| 正常 | 90-120 | 60-80 | 绿色 |
| 正常偏高 | 121-130 | 81-85 | 黄色 |
| 轻度高血压 | 131-140 | 86-90 | 橙色 |
| 中度高血压 | 141-160 | 91-100 | 红色 |
| 重度高血压 | >160 | >100 | 深红色 |

## 使用步骤

### 录入血压数据
1. 点击左侧菜单"健康管理"
2. 点击右上角"录入血压"按钮
3. 在弹出窗口中：
   - 选择日期（默认今天）
   - 输入收缩压和舒张压
   - 可选输入心率
   - 可选添加备注
4. 点击"保存记录"
5. 可继续录入多次血压数据

### 查看血压统计
1. 在健康管理页面下方查看"今日血压统计"区域
2. 查看四个统计卡片的数据
3. 点击刷新按钮更新统计数据

### 管理血压记录
1. 在血压录入窗口右侧查看今日记录
2. 点击记录旁的"删除"按钮可删除错误记录
3. 记录按时间倒序排列，最新记录在最上方

## 注意事项

1. **数据验证**：
   - 血压值必须在合理范围内
   - 收缩压必须大于舒张压
   - 心率为可选项，如输入需在合理范围内

2. **时间记录**：
   - 每次录入自动记录当前时间
   - 时间精确到分钟
   - 同一分钟内的多次录入会覆盖

3. **数据安全**：
   - 所有数据存储在本地
   - 建议定期备份HealthData文件夹
   - 删除操作不可恢复，请谨慎操作

4. **异常提醒**：
   - 血压异常时会显示警告提示
   - 建议异常血压及时咨询医生
   - 统计区域会突出显示异常情况

## 技术实现

### 新增文件
- `Models/BloodPressureRecord.cs` - 血压记录数据模型
- `Services/BloodPressureService.cs` - 血压数据服务
- `AddBloodPressureWindow.xaml` - 血压录入窗口界面
- `AddBloodPressureWindow.xaml.cs` - 血压录入窗口逻辑

### 修改文件
- `MainWindow.xaml` - 添加血压录入按钮和统计区域
- `MainWindow.xaml.cs` - 添加血压相关事件处理方法

### 数据结构
```csv
Date,RecordTime,SystolicPressure,DiastolicPressure,HeartRate,Notes
2024-01-15,2024-01-15 09:30:00,120,80,72,"晨起测量"
2024-01-15,2024-01-15 21:00:00,125,82,75,"睡前测量"
```

## 后续扩展建议

1. **图表展示**：添加血压趋势图表
2. **历史统计**：支持周、月、年度血压统计
3. **目标设定**：设置血压控制目标
4. **提醒功能**：定时提醒测量血压
5. **数据导出**：支持导出血压报告
6. **医生建议**：根据血压数据提供健康建议

## 测试建议

1. **基本功能测试**：
   - 录入正常血压数据
   - 录入异常血压数据
   - 测试数据验证功能

2. **多次录入测试**：
   - 同一天录入多次数据
   - 测试删除功能
   - 验证统计数据准确性

3. **边界值测试**：
   - 测试血压值边界
   - 测试心率边界
   - 测试日期选择

4. **异常情况测试**：
   - 测试网络断开情况
   - 测试文件权限问题
   - 测试大量数据处理
